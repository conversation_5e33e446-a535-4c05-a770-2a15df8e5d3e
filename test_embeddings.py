#!/usr/bin/env python3
"""
Test script to verify embedding services are working
"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_embedder(embedder_type: str):
    """Test a specific embedder type"""
    print(f"\n🧪 Testing {embedder_type} embedder...")
    
    try:
        # Import the embedder classes
        from pdf_embedder import (
            NomicEmbedder, 
            OpenAICompatibleEmbedder, 
            HuggingFaceEmbedder, 
            SentenceTransformersEmbedder,
            TogetherAIEmbedder
        )
        
        # Create embedder based on type
        if embedder_type.lower() == "nomic_api":
            embedder = NomicEmbedder(use_ollama=False)
        elif embedder_type.lower() == "ollama":
            embedder = NomicEmbedder(use_ollama=True)
        elif embedder_type.lower() == "openai":
            embedder = OpenAICompatibleEmbedder()
        elif embedder_type.lower() == "huggingface":
            embedder = HuggingFaceEmbedder()
        elif embedder_type.lower() == "sentence_transformers":
            embedder = SentenceTransformersEmbedder()
        elif embedder_type.lower() == "together":
            embedder = TogetherAIEmbedder()
        else:
            print(f"❌ Unknown embedder type: {embedder_type}")
            return False
        
        # Test with sample text
        test_texts = [
            "What are the symptoms of acidosis in animals?",
            "How to treat respiratory infections in cattle?"
        ]
        
        print(f"   Generating embeddings for {len(test_texts)} test texts...")
        embeddings = embedder.get_embeddings(test_texts)
        
        if embeddings and len(embeddings) == len(test_texts):
            first_embedding = embeddings[0]
            if first_embedding and len(first_embedding) > 0:
                print(f"   ✅ Success! Generated {len(embeddings)} embeddings")
                print(f"   📏 Embedding dimension: {len(first_embedding)}")
                print(f"   📊 Sample values: {first_embedding[:5]}")
                return True
            else:
                print(f"   ❌ Empty embeddings generated")
                return False
        else:
            print(f"   ❌ Failed to generate embeddings")
            return False
            
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False

def test_current_configuration():
    """Test the currently configured embedder"""
    embedder_type = os.getenv("EMBEDDER_TYPE", "nomic")
    print(f"🔍 Testing current configuration: {embedder_type}")
    
    # Show relevant environment variables
    print("\n📋 Current Environment:")
    relevant_vars = [
        "EMBEDDER_TYPE", "NOMIC_API_KEY", "OPENAI_API_KEY", 
        "HUGGINGFACE_API_KEY", "TOGETHER_API_KEY", "EMBEDDING_MODEL"
    ]
    
    for var in relevant_vars:
        value = os.getenv(var)
        if value:
            # Hide API keys for security
            if "API_KEY" in var:
                display_value = value[:8] + "..." if len(value) > 8 else "***"
            else:
                display_value = value
            print(f"   {var}: {display_value}")
    
    return test_embedder(embedder_type)

def test_all_available():
    """Test all available embedding services"""
    print("🧪 Testing all available embedding services...")
    
    services = [
        ("sentence_transformers", "Sentence Transformers (Local)"),
        ("nomic_api", "Nomic AI API"),
        ("openai", "OpenAI API"),
        ("together", "Together AI"),
        ("huggingface", "Hugging Face API"),
        ("ollama", "Local Ollama")
    ]
    
    results = {}
    
    for service_type, service_name in services:
        print(f"\n{'='*50}")
        print(f"Testing: {service_name}")
        print('='*50)
        
        # Check if required API key is available
        required_keys = {
            "nomic_api": "NOMIC_API_KEY",
            "openai": "OPENAI_API_KEY", 
            "huggingface": "HUGGINGFACE_API_KEY",
            "together": "TOGETHER_API_KEY"
        }
        
        if service_type in required_keys:
            api_key = os.getenv(required_keys[service_type])
            if not api_key:
                print(f"   ⏭️  Skipping - No {required_keys[service_type]} found")
                results[service_name] = "Skipped (No API Key)"
                continue
        
        success = test_embedder(service_type)
        results[service_name] = "✅ Working" if success else "❌ Failed"
    
    # Print summary
    print(f"\n{'='*50}")
    print("📊 SUMMARY")
    print('='*50)
    
    for service, status in results.items():
        print(f"   {service}: {status}")
    
    # Recommend best working option
    working_services = [name for name, status in results.items() if "✅" in status]
    if working_services:
        print(f"\n💡 Recommended: Use {working_services[0]}")
        
        # Show how to configure it
        service_configs = {
            "Sentence Transformers (Local)": "EMBEDDER_TYPE=sentence_transformers",
            "Nomic AI API": "EMBEDDER_TYPE=nomic_api",
            "OpenAI API": "EMBEDDER_TYPE=openai",
            "Together AI": "EMBEDDER_TYPE=together"
        }
        
        for service in working_services[:1]:  # Show config for first working service
            if service in service_configs:
                print(f"   Configuration: {service_configs[service]}")
    else:
        print("\n❌ No working embedding services found!")
        print("💡 Try running: python configure_embeddings.py")

def main():
    """Main test function"""
    print("🔗 Embedding Services Test")
    print("=" * 50)
    
    if len(sys.argv) > 1:
        if sys.argv[1] == "--all":
            test_all_available()
        else:
            test_embedder(sys.argv[1])
    else:
        print("Choose test mode:")
        print("1. Test current configuration")
        print("2. Test all available services")
        print("3. Test specific service")
        
        choice = input("\nEnter choice (1-3): ").strip()
        
        if choice == "1":
            success = test_current_configuration()
            if not success:
                print("\n💡 Current configuration not working. Try:")
                print("   python configure_embeddings.py")
        elif choice == "2":
            test_all_available()
        elif choice == "3":
            print("\nAvailable services:")
            print("- nomic_api")
            print("- openai") 
            print("- huggingface")
            print("- sentence_transformers")
            print("- together")
            print("- ollama")
            
            service = input("\nEnter service name: ").strip()
            test_embedder(service)
        else:
            print("❌ Invalid choice")

if __name__ == "__main__":
    main()
