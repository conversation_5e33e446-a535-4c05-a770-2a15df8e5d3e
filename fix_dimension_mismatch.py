#!/usr/bin/env python3
"""
Script to fix vector dimension mismatches in the database
"""

import os
import sys
from dotenv import load_dotenv
from pdf_embedder import PostgreSQLManager, PDFEmbeddingPipeline
import logging

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_current_setup():
    """Check current database and embedder setup"""
    print("🔍 Checking current setup...")
    
    # Check database
    db_manager = PostgreSQLManager()
    current_dim = db_manager.get_embedding_dimension()
    
    if current_dim:
        print(f"📊 Database embedding dimension: {current_dim}")
    else:
        print("📊 No existing database table found")
    
    # Check embedder
    embedder_type = os.getenv("EMBEDDER_TYPE", "nomic")
    print(f"🤖 Current embedder type: {embedder_type}")
    
    try:
        pipeline = PDFEmbeddingPipeline(embedder_type=embedder_type)
        test_embeddings = pipeline.embedder.get_embeddings(["test"])
        if test_embeddings and test_embeddings[0]:
            embedder_dim = len(test_embeddings[0])
            print(f"🎯 Embedder dimension: {embedder_dim}")
            
            if current_dim and current_dim != embedder_dim:
                print(f"⚠️  DIMENSION MISMATCH: Database={current_dim}D, Embedder={embedder_dim}D")
                return False, current_dim, embedder_dim
            else:
                print("✅ Dimensions are compatible!")
                return True, current_dim, embedder_dim
        else:
            print("❌ Failed to test embedder")
            return False, current_dim, None
    except Exception as e:
        print(f"❌ Error testing embedder: {e}")
        return False, current_dim, None

def backup_database():
    """Create a backup of the current database"""
    print("\n💾 Creating database backup...")
    
    db_manager = PostgreSQLManager()
    table_name = db_manager.table_name
    backup_table = f"{table_name}_backup_{int(__import__('time').time())}"
    
    try:
        with db_manager.get_connection() as conn:
            with conn.cursor() as cur:
                # Create backup table
                cur.execute(f"""
                    CREATE TABLE {backup_table} AS 
                    SELECT * FROM {table_name}
                """)
                
                # Get count
                cur.execute(f"SELECT COUNT(*) FROM {backup_table}")
                count = cur.fetchone()[0]
                
                conn.commit()
                print(f"✅ Backup created: {backup_table} ({count} records)")
                return backup_table
                
    except Exception as e:
        print(f"❌ Backup failed: {e}")
        return None

def drop_and_recreate_table(new_dimension: int):
    """Drop the existing table and recreate with new dimension"""
    print(f"\n🔄 Recreating table with {new_dimension} dimensions...")
    
    db_manager = PostgreSQLManager()
    table_name = db_manager.table_name
    
    try:
        with db_manager.get_connection() as conn:
            with conn.cursor() as cur:
                # Drop existing table
                cur.execute(f"DROP TABLE IF EXISTS {table_name}")
                print(f"🗑️  Dropped table: {table_name}")
                
                # Drop existing index
                cur.execute(f"DROP INDEX IF EXISTS {table_name}_embedding_idx")
                cur.execute(f"DROP INDEX IF EXISTS {table_name}_filename_idx")
                
                conn.commit()
        
        # Create new table with correct dimension
        db_manager.create_tables(embedding_dimension=new_dimension)
        print(f"✅ Created new table with {new_dimension} dimensions")
        return True
        
    except Exception as e:
        print(f"❌ Failed to recreate table: {e}")
        return False

def suggest_compatible_models():
    """Suggest embedding models with specific dimensions"""
    print("\n💡 Compatible embedding models:")
    
    print("\n📏 384-dimensional models:")
    print("   - sentence-transformers/all-MiniLM-L6-v2")
    print("   - sentence-transformers/all-MiniLM-L12-v2")
    print("   - sentence-transformers/paraphrase-MiniLM-L6-v2")
    
    print("\n📏 768-dimensional models:")
    print("   - sentence-transformers/all-mpnet-base-v2")
    print("   - nomic-ai/nomic-embed-text-v1.5")
    print("   - sentence-transformers/all-roberta-large-v1")
    
    print("\n📏 1536-dimensional models:")
    print("   - OpenAI text-embedding-3-small")
    print("   - OpenAI text-embedding-ada-002")

def main():
    """Main function"""
    print("🔧 Vector Dimension Mismatch Fixer")
    print("=" * 50)
    
    # Check current setup
    compatible, db_dim, embedder_dim = check_current_setup()
    
    if compatible:
        print("\n✅ No dimension mismatch found!")
        return
    
    if not embedder_dim:
        print("\n❌ Cannot determine embedder dimension. Please check your configuration.")
        return
    
    print(f"\n⚠️  Found dimension mismatch:")
    print(f"   Database: {db_dim} dimensions")
    print(f"   Embedder: {embedder_dim} dimensions")
    
    print("\n🔧 Available solutions:")
    print("1. 🔄 Recreate database table (DELETES ALL DATA)")
    print("2. 🎯 Change embedding model to match database")
    print("3. 💡 Show compatible models")
    print("4. ❌ Exit")
    
    choice = input("\nChoose solution (1-4): ").strip()
    
    if choice == "1":
        print(f"\n⚠️  WARNING: This will DELETE ALL existing embeddings!")
        print(f"   Current database has {db_dim}D vectors")
        print(f"   Will recreate with {embedder_dim}D vectors")
        
        confirm = input("\nType 'DELETE' to confirm: ").strip()
        if confirm == "DELETE":
            # Create backup first
            backup_table = backup_database()
            if backup_table:
                print(f"📁 Data backed up to: {backup_table}")
            
            # Recreate table
            if drop_and_recreate_table(embedder_dim):
                print("\n✅ Table recreated successfully!")
                print("🔄 You can now run your PDF processing again.")
            else:
                print("\n❌ Failed to recreate table")
        else:
            print("❌ Operation cancelled")
    
    elif choice == "2":
        print(f"\n🎯 To match your database ({db_dim}D), update your .env file:")
        
        if db_dim == 384:
            print("   EMBEDDER_TYPE=sentence_transformers")
            print("   EMBEDDING_MODEL=all-MiniLM-L6-v2")
        elif db_dim == 768:
            print("   EMBEDDER_TYPE=sentence_transformers")
            print("   EMBEDDING_MODEL=all-mpnet-base-v2")
            print("   # OR")
            print("   EMBEDDER_TYPE=nomic_api")
            print("   NOMIC_API_KEY=your_key")
        elif db_dim == 1536:
            print("   EMBEDDER_TYPE=openai")
            print("   OPENAI_API_KEY=your_key")
            print("   EMBEDDING_MODEL=text-embedding-3-small")
        else:
            print(f"   No standard models found for {db_dim} dimensions")
    
    elif choice == "3":
        suggest_compatible_models()
    
    elif choice == "4":
        print("👋 Exiting...")
    
    else:
        print("❌ Invalid choice")

if __name__ == "__main__":
    main()
