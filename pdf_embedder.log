2025-06-09 18:32:10,230 - pdf_embedder - INFO - Initializing PDFEmbeddingPipeline with embedder type: nomic
2025-06-09 18:32:10,230 - pdf_embedder - INFO - Initialized NomicEmbedder with Ollama - base_url: http://localhost:11434, model: nomic-embed-text:latest
2025-06-09 18:32:10,231 - pdf_embedder - INFO - Initialized PDFProcessor with chunk_size: 1000, chunk_overlap: 200
2025-06-09 18:32:10,231 - pdf_embedder - INFO - Using DATABASE_URL for database connection (production mode)
2025-06-09 18:32:10,231 - pdf_embedder - INFO - Using table name: documents
2025-06-09 18:32:10,231 - pdf_embedder - INFO - PDFEmbeddingPipeline initialized successfully
2025-06-09 18:32:10,231 - pdf_embedder - INFO - Starting to process PDF folder: diseases
2025-06-09 18:32:10,231 - pdf_embedder - INFO - Creating tables for documents...
2025-06-09 18:32:12,879 - pdf_embedder - INFO - Database connection established successfully on attempt 1
2025-06-09 18:32:13,466 - pdf_embedder - INFO - Tables created successfully!
2025-06-09 18:32:13,469 - pdf_embedder - INFO - Found 34 PDF files in diseases
2025-06-09 18:32:13,469 - pdf_embedder - INFO - Processing file 1/34: DYSENTERY.pdf
2025-06-09 18:32:13,469 - pdf_embedder - INFO - Processing single PDF: DYSENTERY.pdf
2025-06-09 18:32:13,469 - pdf_embedder - INFO - Extracting text from PDF: diseases/DYSENTERY.pdf
2025-06-09 18:32:13,499 - pdf_embedder - INFO - Successfully extracted 815 characters from diseases/DYSENTERY.pdf
2025-06-09 18:32:13,500 - pdf_embedder - INFO - Created 5 chunks from DYSENTERY.pdf
2025-06-09 18:32:13,500 - pdf_embedder - INFO - Generating embeddings for 5 texts using Ollama
2025-06-09 18:32:14,294 - pdf_embedder - INFO - Generated 5 embeddings total
2025-06-09 18:32:14,295 - pdf_embedder - INFO - Inserting 5 document chunks into database...
2025-06-09 18:32:16,533 - pdf_embedder - INFO - Database connection established successfully on attempt 1
2025-06-09 18:32:17,713 - pdf_embedder - INFO - Successfully inserted 5 document chunks
2025-06-09 18:32:17,714 - pdf_embedder - INFO - Successfully processed and stored 5 chunks for DYSENTERY.pdf
2025-06-09 18:32:17,714 - pdf_embedder - INFO - Processing file 2/34: MILK FEVER.pdf
2025-06-09 18:32:17,714 - pdf_embedder - INFO - Processing single PDF: MILK FEVER.pdf
2025-06-09 18:32:17,715 - pdf_embedder - INFO - Extracting text from PDF: diseases/MILK FEVER.pdf
2025-06-09 18:32:17,724 - pdf_embedder - INFO - Successfully extracted 688 characters from diseases/MILK FEVER.pdf
2025-06-09 18:32:17,724 - pdf_embedder - INFO - Created 6 chunks from MILK FEVER.pdf
2025-06-09 18:32:17,725 - pdf_embedder - INFO - Generating embeddings for 6 texts using Ollama
2025-06-09 18:32:17,924 - pdf_embedder - INFO - Generated 6 embeddings total
2025-06-09 18:32:17,924 - pdf_embedder - INFO - Inserting 6 document chunks into database...
2025-06-09 18:32:20,268 - pdf_embedder - INFO - Database connection established successfully on attempt 1
2025-06-09 18:32:21,763 - pdf_embedder - INFO - Successfully inserted 6 document chunks
2025-06-09 18:32:21,764 - pdf_embedder - INFO - Successfully processed and stored 6 chunks for MILK FEVER.pdf
2025-06-09 18:32:21,765 - pdf_embedder - INFO - Processing file 3/34: Dry Period Mastitis.pdf
2025-06-09 18:32:21,765 - pdf_embedder - INFO - Processing single PDF: Dry Period Mastitis.pdf
2025-06-09 18:32:21,765 - pdf_embedder - INFO - Extracting text from PDF: diseases/Dry Period Mastitis.pdf
2025-06-09 18:32:21,785 - pdf_embedder - INFO - Successfully extracted 317 characters from diseases/Dry Period Mastitis.pdf
2025-06-09 18:32:21,785 - pdf_embedder - INFO - Created 1 chunks from Dry Period Mastitis.pdf
2025-06-09 18:32:21,785 - pdf_embedder - INFO - Generating embeddings for 1 texts using Ollama
2025-06-09 18:32:21,861 - pdf_embedder - INFO - Generated 1 embeddings total
2025-06-09 18:32:21,861 - pdf_embedder - INFO - Inserting 1 document chunks into database...
2025-06-09 18:32:24,202 - pdf_embedder - INFO - Database connection established successfully on attempt 1
2025-06-09 18:32:25,080 - pdf_embedder - INFO - Successfully inserted 1 document chunks
2025-06-09 18:32:25,082 - pdf_embedder - INFO - Successfully processed and stored 1 chunks for Dry Period Mastitis.pdf
2025-06-09 18:32:25,082 - pdf_embedder - INFO - Processing file 4/34: PNEUMONIA.pdf
2025-06-09 18:32:25,083 - pdf_embedder - INFO - Processing single PDF: PNEUMONIA.pdf
2025-06-09 18:32:25,083 - pdf_embedder - INFO - Extracting text from PDF: diseases/PNEUMONIA.pdf
2025-06-09 18:32:25,097 - pdf_embedder - INFO - Successfully extracted 955 characters from diseases/PNEUMONIA.pdf
2025-06-09 18:32:25,097 - pdf_embedder - INFO - Created 6 chunks from PNEUMONIA.pdf
2025-06-09 18:32:25,097 - pdf_embedder - INFO - Generating embeddings for 6 texts using Ollama
2025-06-09 18:32:25,308 - pdf_embedder - INFO - Generated 6 embeddings total
2025-06-09 18:32:25,308 - pdf_embedder - INFO - Inserting 6 document chunks into database...
2025-06-09 18:32:27,557 - pdf_embedder - INFO - Database connection established successfully on attempt 1
2025-06-09 18:32:28,941 - pdf_embedder - INFO - Successfully inserted 6 document chunks
2025-06-09 18:32:28,942 - pdf_embedder - INFO - Successfully processed and stored 6 chunks for PNEUMONIA.pdf
2025-06-09 18:32:28,942 - pdf_embedder - INFO - Processing file 5/34: THEILERIOSIS.pdf
2025-06-09 18:32:28,942 - pdf_embedder - INFO - Processing single PDF: THEILERIOSIS.pdf
2025-06-09 18:32:28,942 - pdf_embedder - INFO - Extracting text from PDF: diseases/THEILERIOSIS.pdf
2025-06-09 18:32:28,950 - pdf_embedder - INFO - Successfully extracted 2020 characters from diseases/THEILERIOSIS.pdf
2025-06-09 18:32:28,950 - pdf_embedder - INFO - Created 10 chunks from THEILERIOSIS.pdf
2025-06-09 18:32:28,950 - pdf_embedder - INFO - Generating embeddings for 10 texts using Ollama
2025-06-09 18:32:29,292 - pdf_embedder - INFO - Generated 10 embeddings total
2025-06-09 18:32:29,292 - pdf_embedder - INFO - Inserting 10 document chunks into database...
2025-06-09 18:32:31,596 - pdf_embedder - INFO - Database connection established successfully on attempt 1
2025-06-09 18:32:33,310 - pdf_embedder - INFO - Successfully inserted 10 document chunks
2025-06-09 18:32:33,311 - pdf_embedder - INFO - Successfully processed and stored 10 chunks for THEILERIOSIS.pdf
2025-06-09 18:32:33,311 - pdf_embedder - INFO - Processing file 6/34: JAUNDICE.pdf
2025-06-09 18:32:33,311 - pdf_embedder - INFO - Processing single PDF: JAUNDICE.pdf
2025-06-09 18:32:33,312 - pdf_embedder - INFO - Extracting text from PDF: diseases/JAUNDICE.pdf
2025-06-09 18:32:33,323 - pdf_embedder - INFO - Successfully extracted 662 characters from diseases/JAUNDICE.pdf
2025-06-09 18:32:33,324 - pdf_embedder - INFO - Created 5 chunks from JAUNDICE.pdf
2025-06-09 18:32:33,324 - pdf_embedder - INFO - Generating embeddings for 5 texts using Ollama
2025-06-09 18:32:33,515 - pdf_embedder - INFO - Generated 5 embeddings total
2025-06-09 18:32:33,515 - pdf_embedder - INFO - Inserting 5 document chunks into database...
2025-06-09 18:32:35,797 - pdf_embedder - INFO - Database connection established successfully on attempt 1
2025-06-09 18:32:37,008 - pdf_embedder - INFO - Successfully inserted 5 document chunks
2025-06-09 18:32:37,010 - pdf_embedder - INFO - Successfully processed and stored 5 chunks for JAUNDICE.pdf
2025-06-09 18:32:37,010 - pdf_embedder - INFO - Processing file 7/34: TRYPANOSOMIASIS.pdf
2025-06-09 18:32:37,010 - pdf_embedder - INFO - Processing single PDF: TRYPANOSOMIASIS.pdf
2025-06-09 18:32:37,011 - pdf_embedder - INFO - Extracting text from PDF: diseases/TRYPANOSOMIASIS.pdf
2025-06-09 18:32:37,025 - pdf_embedder - INFO - Successfully extracted 854 characters from diseases/TRYPANOSOMIASIS.pdf
2025-06-09 18:32:37,025 - pdf_embedder - INFO - Created 6 chunks from TRYPANOSOMIASIS.pdf
2025-06-09 18:32:37,025 - pdf_embedder - INFO - Generating embeddings for 6 texts using Ollama
2025-06-09 18:32:37,241 - pdf_embedder - INFO - Generated 6 embeddings total
2025-06-09 18:32:37,242 - pdf_embedder - INFO - Inserting 6 document chunks into database...
2025-06-09 18:32:39,578 - pdf_embedder - INFO - Database connection established successfully on attempt 1
2025-06-09 18:32:41,083 - pdf_embedder - INFO - Successfully inserted 6 document chunks
2025-06-09 18:32:41,085 - pdf_embedder - INFO - Successfully processed and stored 6 chunks for TRYPANOSOMIASIS.pdf
2025-06-09 18:32:41,085 - pdf_embedder - INFO - Processing file 8/34: LISTERIOSIS.pdf
2025-06-09 18:32:41,086 - pdf_embedder - INFO - Processing single PDF: LISTERIOSIS.pdf
2025-06-09 18:32:41,086 - pdf_embedder - INFO - Extracting text from PDF: diseases/LISTERIOSIS.pdf
2025-06-09 18:32:41,102 - pdf_embedder - INFO - Successfully extracted 1787 characters from diseases/LISTERIOSIS.pdf
2025-06-09 18:32:41,103 - pdf_embedder - INFO - Created 7 chunks from LISTERIOSIS.pdf
2025-06-09 18:32:41,103 - pdf_embedder - INFO - Generating embeddings for 7 texts using Ollama
2025-06-09 18:32:41,386 - pdf_embedder - INFO - Generated 7 embeddings total
2025-06-09 18:32:41,386 - pdf_embedder - INFO - Inserting 7 document chunks into database...
2025-06-09 18:32:43,696 - pdf_embedder - INFO - Database connection established successfully on attempt 1
2025-06-09 18:32:45,159 - pdf_embedder - INFO - Successfully inserted 7 document chunks
2025-06-09 18:32:45,160 - pdf_embedder - INFO - Successfully processed and stored 7 chunks for LISTERIOSIS.pdf
2025-06-09 18:32:45,160 - pdf_embedder - INFO - Processing file 9/34: BABESIA.pdf
2025-06-09 18:32:45,160 - pdf_embedder - INFO - Processing single PDF: BABESIA.pdf
2025-06-09 18:32:45,161 - pdf_embedder - INFO - Extracting text from PDF: diseases/BABESIA.pdf
2025-06-09 18:32:45,173 - pdf_embedder - INFO - Successfully extracted 1368 characters from diseases/BABESIA.pdf
2025-06-09 18:32:45,173 - pdf_embedder - INFO - Created 7 chunks from BABESIA.pdf
2025-06-09 18:32:45,173 - pdf_embedder - INFO - Generating embeddings for 7 texts using Ollama
2025-06-09 18:32:45,473 - pdf_embedder - INFO - Generated 7 embeddings total
2025-06-09 18:32:45,474 - pdf_embedder - INFO - Inserting 7 document chunks into database...
2025-06-09 18:32:47,800 - pdf_embedder - INFO - Database connection established successfully on attempt 1
2025-06-09 18:32:49,282 - pdf_embedder - INFO - Successfully inserted 7 document chunks
2025-06-09 18:32:49,284 - pdf_embedder - INFO - Successfully processed and stored 7 chunks for BABESIA.pdf
2025-06-09 18:32:49,284 - pdf_embedder - INFO - Processing file 10/34: UTERINE TORSION.pdf
2025-06-09 18:32:49,284 - pdf_embedder - INFO - Processing single PDF: UTERINE TORSION.pdf
2025-06-09 18:32:49,284 - pdf_embedder - INFO - Extracting text from PDF: diseases/UTERINE TORSION.pdf
2025-06-09 18:32:49,302 - pdf_embedder - INFO - Successfully extracted 2220 characters from diseases/UTERINE TORSION.pdf
2025-06-09 18:32:49,303 - pdf_embedder - INFO - Created 10 chunks from UTERINE TORSION.pdf
2025-06-09 18:32:49,303 - pdf_embedder - INFO - Generating embeddings for 10 texts using Ollama
2025-06-09 18:32:49,700 - pdf_embedder - INFO - Generated 10 embeddings total
2025-06-09 18:32:49,701 - pdf_embedder - INFO - Inserting 10 document chunks into database...
2025-06-09 18:32:52,032 - pdf_embedder - INFO - Database connection established successfully on attempt 1
2025-06-09 18:32:53,797 - pdf_embedder - INFO - Successfully inserted 10 document chunks
2025-06-09 18:32:53,799 - pdf_embedder - INFO - Successfully processed and stored 10 chunks for UTERINE TORSION.pdf
2025-06-09 18:32:53,799 - pdf_embedder - INFO - Processing file 11/34: Chronic_Fibrosis Mastitis.pdf
2025-06-09 18:32:53,799 - pdf_embedder - INFO - Processing single PDF: Chronic_Fibrosis Mastitis.pdf
2025-06-09 18:32:53,799 - pdf_embedder - INFO - Extracting text from PDF: diseases/Chronic_Fibrosis Mastitis.pdf
2025-06-09 18:32:53,810 - pdf_embedder - INFO - Successfully extracted 444 characters from diseases/Chronic_Fibrosis Mastitis.pdf
2025-06-09 18:32:53,810 - pdf_embedder - INFO - Created 1 chunks from Chronic_Fibrosis Mastitis.pdf
2025-06-09 18:32:53,810 - pdf_embedder - INFO - Generating embeddings for 1 texts using Ollama
2025-06-09 18:32:53,907 - pdf_embedder - INFO - Generated 1 embeddings total
2025-06-09 18:32:53,907 - pdf_embedder - INFO - Inserting 1 document chunks into database...
2025-06-09 18:32:56,196 - pdf_embedder - INFO - Database connection established successfully on attempt 1
2025-06-09 18:32:57,154 - pdf_embedder - INFO - Successfully inserted 1 document chunks
2025-06-09 18:32:57,155 - pdf_embedder - INFO - Successfully processed and stored 1 chunks for Chronic_Fibrosis Mastitis.pdf
2025-06-09 18:32:57,156 - pdf_embedder - INFO - Processing file 12/34: _NAVAL ILL.pdf
2025-06-09 18:32:57,156 - pdf_embedder - INFO - Processing single PDF: _NAVAL ILL.pdf
2025-06-09 18:32:57,156 - pdf_embedder - INFO - Extracting text from PDF: diseases/_NAVAL ILL.pdf
2025-06-09 18:32:57,167 - pdf_embedder - INFO - Successfully extracted 1101 characters from diseases/_NAVAL ILL.pdf
2025-06-09 18:32:57,167 - pdf_embedder - INFO - Created 5 chunks from _NAVAL ILL.pdf
2025-06-09 18:32:57,167 - pdf_embedder - INFO - Generating embeddings for 5 texts using Ollama
2025-06-09 18:32:57,387 - pdf_embedder - INFO - Generated 5 embeddings total
2025-06-09 18:32:57,387 - pdf_embedder - INFO - Inserting 5 document chunks into database...
2025-06-09 18:32:59,628 - pdf_embedder - INFO - Database connection established successfully on attempt 1
2025-06-09 18:33:00,791 - pdf_embedder - INFO - Successfully inserted 5 document chunks
2025-06-09 18:33:00,792 - pdf_embedder - INFO - Successfully processed and stored 5 chunks for _NAVAL ILL.pdf
2025-06-09 18:33:00,792 - pdf_embedder - INFO - Processing file 13/34: Subclinical Mastitis.pdf
2025-06-09 18:33:00,792 - pdf_embedder - INFO - Processing single PDF: Subclinical Mastitis.pdf
2025-06-09 18:33:00,792 - pdf_embedder - INFO - Extracting text from PDF: diseases/Subclinical Mastitis.pdf
2025-06-09 18:33:00,797 - pdf_embedder - INFO - Successfully extracted 675 characters from diseases/Subclinical Mastitis.pdf
2025-06-09 18:33:00,797 - pdf_embedder - INFO - Created 1 chunks from Subclinical Mastitis.pdf
2025-06-09 18:33:00,797 - pdf_embedder - INFO - Generating embeddings for 1 texts using Ollama
2025-06-09 18:33:00,881 - pdf_embedder - INFO - Generated 1 embeddings total
2025-06-09 18:33:00,881 - pdf_embedder - INFO - Inserting 1 document chunks into database...
2025-06-09 18:33:03,244 - pdf_embedder - INFO - Database connection established successfully on attempt 1
2025-06-09 18:33:04,153 - pdf_embedder - INFO - Successfully inserted 1 document chunks
2025-06-09 18:33:04,154 - pdf_embedder - INFO - Successfully processed and stored 1 chunks for Subclinical Mastitis.pdf
2025-06-09 18:33:04,154 - pdf_embedder - INFO - Processing file 14/34: ACIDOSIS.pdf
2025-06-09 18:33:04,154 - pdf_embedder - INFO - Processing single PDF: ACIDOSIS.pdf
2025-06-09 18:33:04,154 - pdf_embedder - INFO - Extracting text from PDF: diseases/ACIDOSIS.pdf
2025-06-09 18:33:04,165 - pdf_embedder - INFO - Successfully extracted 1218 characters from diseases/ACIDOSIS.pdf
2025-06-09 18:33:04,165 - pdf_embedder - INFO - Created 4 chunks from ACIDOSIS.pdf
2025-06-09 18:33:04,166 - pdf_embedder - INFO - Generating embeddings for 4 texts using Ollama
2025-06-09 18:33:04,348 - pdf_embedder - INFO - Generated 4 embeddings total
2025-06-09 18:33:04,349 - pdf_embedder - INFO - Inserting 4 document chunks into database...
2025-06-09 18:33:06,684 - pdf_embedder - INFO - Database connection established successfully on attempt 1
2025-06-09 18:33:07,838 - pdf_embedder - INFO - Successfully inserted 4 document chunks
2025-06-09 18:33:07,841 - pdf_embedder - INFO - Successfully processed and stored 4 chunks for ACIDOSIS.pdf
2025-06-09 18:33:07,841 - pdf_embedder - INFO - Processing file 15/34: Per Acute Mastitis.pdf
2025-06-09 18:33:07,842 - pdf_embedder - INFO - Processing single PDF: Per Acute Mastitis.pdf
2025-06-09 18:33:07,842 - pdf_embedder - INFO - Extracting text from PDF: diseases/Per Acute Mastitis.pdf
2025-06-09 18:33:07,849 - pdf_embedder - INFO - Successfully extracted 540 characters from diseases/Per Acute Mastitis.pdf
2025-06-09 18:33:07,850 - pdf_embedder - INFO - Created 1 chunks from Per Acute Mastitis.pdf
2025-06-09 18:33:07,850 - pdf_embedder - INFO - Generating embeddings for 1 texts using Ollama
2025-06-09 18:33:07,950 - pdf_embedder - INFO - Generated 1 embeddings total
2025-06-09 18:33:07,950 - pdf_embedder - INFO - Inserting 1 document chunks into database...
2025-06-09 18:33:10,279 - pdf_embedder - INFO - Database connection established successfully on attempt 1
2025-06-09 18:33:11,163 - pdf_embedder - INFO - Successfully inserted 1 document chunks
2025-06-09 18:33:11,164 - pdf_embedder - INFO - Successfully processed and stored 1 chunks for Per Acute Mastitis.pdf
2025-06-09 18:33:11,164 - pdf_embedder - INFO - Processing file 16/34: DIARRHEA_ENTERITIS.pdf
2025-06-09 18:33:11,164 - pdf_embedder - INFO - Processing single PDF: DIARRHEA_ENTERITIS.pdf
2025-06-09 18:33:11,164 - pdf_embedder - INFO - Extracting text from PDF: diseases/DIARRHEA_ENTERITIS.pdf
2025-06-09 18:33:11,179 - pdf_embedder - INFO - Successfully extracted 1741 characters from diseases/DIARRHEA_ENTERITIS.pdf
2025-06-09 18:33:11,180 - pdf_embedder - INFO - Created 10 chunks from DIARRHEA_ENTERITIS.pdf
2025-06-09 18:33:11,180 - pdf_embedder - INFO - Generating embeddings for 10 texts using Ollama
2025-06-09 18:33:11,555 - pdf_embedder - INFO - Generated 10 embeddings total
2025-06-09 18:33:11,555 - pdf_embedder - INFO - Inserting 10 document chunks into database...
2025-06-09 18:33:14,020 - pdf_embedder - INFO - Database connection established successfully on attempt 1
2025-06-09 18:33:15,776 - pdf_embedder - INFO - Successfully inserted 10 document chunks
2025-06-09 18:33:15,776 - pdf_embedder - INFO - Successfully processed and stored 10 chunks for DIARRHEA_ENTERITIS.pdf
2025-06-09 18:33:15,776 - pdf_embedder - INFO - Processing file 17/34: Summer Mastitis.pdf
2025-06-09 18:33:15,776 - pdf_embedder - INFO - Processing single PDF: Summer Mastitis.pdf
2025-06-09 18:33:15,776 - pdf_embedder - INFO - Extracting text from PDF: diseases/Summer Mastitis.pdf
2025-06-09 18:33:15,781 - pdf_embedder - INFO - Successfully extracted 476 characters from diseases/Summer Mastitis.pdf
2025-06-09 18:33:15,781 - pdf_embedder - INFO - Created 1 chunks from Summer Mastitis.pdf
2025-06-09 18:33:15,781 - pdf_embedder - INFO - Generating embeddings for 1 texts using Ollama
2025-06-09 18:33:15,869 - pdf_embedder - INFO - Generated 1 embeddings total
2025-06-09 18:33:15,869 - pdf_embedder - INFO - Inserting 1 document chunks into database...
2025-06-09 18:33:18,149 - pdf_embedder - INFO - Database connection established successfully on attempt 1
2025-06-09 18:33:19,023 - pdf_embedder - INFO - Successfully inserted 1 document chunks
2025-06-09 18:33:19,024 - pdf_embedder - INFO - Successfully processed and stored 1 chunks for Summer Mastitis.pdf
2025-06-09 18:33:19,024 - pdf_embedder - INFO - Processing file 18/34: ANEMIA.pdf
2025-06-09 18:33:19,025 - pdf_embedder - INFO - Processing single PDF: ANEMIA.pdf
2025-06-09 18:33:19,025 - pdf_embedder - INFO - Extracting text from PDF: diseases/ANEMIA.pdf
2025-06-09 18:33:19,038 - pdf_embedder - INFO - Successfully extracted 1124 characters from diseases/ANEMIA.pdf
2025-06-09 18:33:19,038 - pdf_embedder - INFO - Created 7 chunks from ANEMIA.pdf
2025-06-09 18:33:19,039 - pdf_embedder - INFO - Generating embeddings for 7 texts using Ollama
2025-06-09 18:33:19,297 - pdf_embedder - INFO - Generated 7 embeddings total
2025-06-09 18:33:19,297 - pdf_embedder - INFO - Inserting 7 document chunks into database...
2025-06-09 18:33:21,657 - pdf_embedder - INFO - Database connection established successfully on attempt 1
2025-06-09 18:33:23,142 - pdf_embedder - INFO - Successfully inserted 7 document chunks
2025-06-09 18:33:23,145 - pdf_embedder - INFO - Successfully processed and stored 7 chunks for ANEMIA.pdf
2025-06-09 18:33:23,146 - pdf_embedder - INFO - Processing file 19/34: DYSTOCIA COMPLICATION.pdf
2025-06-09 18:33:23,146 - pdf_embedder - INFO - Processing single PDF: DYSTOCIA COMPLICATION.pdf
2025-06-09 18:33:23,147 - pdf_embedder - INFO - Extracting text from PDF: diseases/DYSTOCIA COMPLICATION.pdf
2025-06-09 18:33:23,161 - pdf_embedder - INFO - Successfully extracted 936 characters from diseases/DYSTOCIA COMPLICATION.pdf
2025-06-09 18:33:23,162 - pdf_embedder - INFO - Created 4 chunks from DYSTOCIA COMPLICATION.pdf
2025-06-09 18:33:23,162 - pdf_embedder - INFO - Generating embeddings for 4 texts using Ollama
2025-06-09 18:33:23,405 - pdf_embedder - INFO - Generated 4 embeddings total
2025-06-09 18:33:23,405 - pdf_embedder - INFO - Inserting 4 document chunks into database...
2025-06-09 18:33:25,671 - pdf_embedder - INFO - Database connection established successfully on attempt 1
2025-06-09 18:33:26,798 - pdf_embedder - INFO - Successfully inserted 4 document chunks
2025-06-09 18:33:26,798 - pdf_embedder - INFO - Successfully processed and stored 4 chunks for DYSTOCIA COMPLICATION.pdf
2025-06-09 18:33:26,799 - pdf_embedder - INFO - Processing file 20/34: Gangrenous Mastitis.pdf
2025-06-09 18:33:26,799 - pdf_embedder - INFO - Processing single PDF: Gangrenous Mastitis.pdf
2025-06-09 18:33:26,799 - pdf_embedder - INFO - Extracting text from PDF: diseases/Gangrenous Mastitis.pdf
2025-06-09 18:33:26,806 - pdf_embedder - INFO - Successfully extracted 531 characters from diseases/Gangrenous Mastitis.pdf
2025-06-09 18:33:26,806 - pdf_embedder - INFO - Created 1 chunks from Gangrenous Mastitis.pdf
2025-06-09 18:33:26,806 - pdf_embedder - INFO - Generating embeddings for 1 texts using Ollama
2025-06-09 18:33:26,907 - pdf_embedder - INFO - Generated 1 embeddings total
2025-06-09 18:33:26,907 - pdf_embedder - INFO - Inserting 1 document chunks into database...
2025-06-09 18:33:29,184 - pdf_embedder - INFO - Database connection established successfully on attempt 1
2025-06-09 18:33:30,053 - pdf_embedder - INFO - Successfully inserted 1 document chunks
2025-06-09 18:33:30,055 - pdf_embedder - INFO - Successfully processed and stored 1 chunks for Gangrenous Mastitis.pdf
2025-06-09 18:33:30,055 - pdf_embedder - INFO - Processing file 21/34: Mild Mastitis.pdf
2025-06-09 18:33:30,055 - pdf_embedder - INFO - Processing single PDF: Mild Mastitis.pdf
2025-06-09 18:33:30,055 - pdf_embedder - INFO - Extracting text from PDF: diseases/Mild Mastitis.pdf
2025-06-09 18:33:30,064 - pdf_embedder - INFO - Successfully extracted 500 characters from diseases/Mild Mastitis.pdf
2025-06-09 18:33:30,065 - pdf_embedder - INFO - Created 1 chunks from Mild Mastitis.pdf
2025-06-09 18:33:30,065 - pdf_embedder - INFO - Generating embeddings for 1 texts using Ollama
2025-06-09 18:33:30,160 - pdf_embedder - INFO - Generated 1 embeddings total
2025-06-09 18:33:30,161 - pdf_embedder - INFO - Inserting 1 document chunks into database...
2025-06-09 18:33:32,558 - pdf_embedder - INFO - Database connection established successfully on attempt 1
2025-06-09 18:33:33,411 - pdf_embedder - INFO - Successfully inserted 1 document chunks
2025-06-09 18:33:33,412 - pdf_embedder - INFO - Successfully processed and stored 1 chunks for Mild Mastitis.pdf
2025-06-09 18:33:33,412 - pdf_embedder - INFO - Processing file 22/34: EYE INFECTION.pdf
2025-06-09 18:33:33,413 - pdf_embedder - INFO - Processing single PDF: EYE INFECTION.pdf
2025-06-09 18:33:33,413 - pdf_embedder - INFO - Extracting text from PDF: diseases/EYE INFECTION.pdf
2025-06-09 18:33:33,427 - pdf_embedder - INFO - Successfully extracted 2131 characters from diseases/EYE INFECTION.pdf
2025-06-09 18:33:33,428 - pdf_embedder - INFO - Created 21 chunks from EYE INFECTION.pdf
2025-06-09 18:33:33,428 - pdf_embedder - INFO - Generating embeddings for 21 texts using Ollama
2025-06-09 18:33:34,094 - pdf_embedder - INFO - Generated 21 embeddings total
2025-06-09 18:33:34,094 - pdf_embedder - INFO - Inserting 21 document chunks into database...
2025-06-09 18:33:36,358 - pdf_embedder - INFO - Database connection established successfully on attempt 1
2025-06-09 18:33:38,438 - pdf_embedder - INFO - Successfully inserted 21 document chunks
2025-06-09 18:33:38,439 - pdf_embedder - INFO - Successfully processed and stored 21 chunks for EYE INFECTION.pdf
2025-06-09 18:33:38,439 - pdf_embedder - INFO - Processing file 23/34: TYMPANY_BLOAT.pdf
2025-06-09 18:33:38,440 - pdf_embedder - INFO - Processing single PDF: TYMPANY_BLOAT.pdf
2025-06-09 18:33:38,440 - pdf_embedder - INFO - Extracting text from PDF: diseases/TYMPANY_BLOAT.pdf
2025-06-09 18:33:38,454 - pdf_embedder - INFO - Successfully extracted 1275 characters from diseases/TYMPANY_BLOAT.pdf
2025-06-09 18:33:38,454 - pdf_embedder - INFO - Created 10 chunks from TYMPANY_BLOAT.pdf
2025-06-09 18:33:38,454 - pdf_embedder - INFO - Generating embeddings for 10 texts using Ollama
2025-06-09 18:33:38,804 - pdf_embedder - INFO - Generated 10 embeddings total
2025-06-09 18:33:38,804 - pdf_embedder - INFO - Inserting 10 document chunks into database...
2025-06-09 18:33:41,044 - pdf_embedder - INFO - Database connection established successfully on attempt 1
2025-06-09 18:33:42,470 - pdf_embedder - INFO - Successfully inserted 10 document chunks
2025-06-09 18:33:42,472 - pdf_embedder - INFO - Successfully processed and stored 10 chunks for TYMPANY_BLOAT.pdf
2025-06-09 18:33:42,472 - pdf_embedder - INFO - Processing file 24/34: SUB CLINICAL KETOSIS, KETOSIS & NERVINE KETOSIS.pdf
2025-06-09 18:33:42,472 - pdf_embedder - INFO - Processing single PDF: SUB CLINICAL KETOSIS, KETOSIS & NERVINE KETOSIS.pdf
2025-06-09 18:33:42,472 - pdf_embedder - INFO - Extracting text from PDF: diseases/SUB CLINICAL KETOSIS, KETOSIS & NERVINE KETOSIS.pdf
2025-06-09 18:33:42,529 - pdf_embedder - INFO - Successfully extracted 931 characters from diseases/SUB CLINICAL KETOSIS, KETOSIS & NERVINE KETOSIS.pdf
2025-06-09 18:33:42,529 - pdf_embedder - INFO - Created 5 chunks from SUB CLINICAL KETOSIS, KETOSIS & NERVINE KETOSIS.pdf
2025-06-09 18:33:42,529 - pdf_embedder - INFO - Generating embeddings for 5 texts using Ollama
2025-06-09 18:33:42,741 - pdf_embedder - INFO - Generated 5 embeddings total
2025-06-09 18:33:42,742 - pdf_embedder - INFO - Inserting 5 document chunks into database...
2025-06-09 18:33:45,012 - pdf_embedder - INFO - Database connection established successfully on attempt 1
2025-06-09 18:33:46,151 - pdf_embedder - INFO - Successfully inserted 5 document chunks
2025-06-09 18:33:46,151 - pdf_embedder - INFO - Successfully processed and stored 5 chunks for SUB CLINICAL KETOSIS, KETOSIS & NERVINE KETOSIS.pdf
2025-06-09 18:33:46,152 - pdf_embedder - INFO - Processing file 25/34: RETENTION OF PLACENTA.pdf
2025-06-09 18:33:46,152 - pdf_embedder - INFO - Processing single PDF: RETENTION OF PLACENTA.pdf
2025-06-09 18:33:46,152 - pdf_embedder - INFO - Extracting text from PDF: diseases/RETENTION OF PLACENTA.pdf
2025-06-09 18:33:46,160 - pdf_embedder - INFO - Successfully extracted 1497 characters from diseases/RETENTION OF PLACENTA.pdf
2025-06-09 18:33:46,160 - pdf_embedder - INFO - Created 8 chunks from RETENTION OF PLACENTA.pdf
2025-06-09 18:33:46,160 - pdf_embedder - INFO - Generating embeddings for 8 texts using Ollama
2025-06-09 18:33:46,451 - pdf_embedder - INFO - Generated 8 embeddings total
2025-06-09 18:33:46,452 - pdf_embedder - INFO - Inserting 8 document chunks into database...
2025-06-09 18:33:48,714 - pdf_embedder - INFO - Database connection established successfully on attempt 1
2025-06-09 18:33:50,128 - pdf_embedder - INFO - Successfully inserted 8 document chunks
2025-06-09 18:33:50,129 - pdf_embedder - INFO - Successfully processed and stored 8 chunks for RETENTION OF PLACENTA.pdf
2025-06-09 18:33:50,130 - pdf_embedder - INFO - Processing file 26/34: STRESS.pdf
2025-06-09 18:33:50,130 - pdf_embedder - INFO - Processing single PDF: STRESS.pdf
2025-06-09 18:33:50,130 - pdf_embedder - INFO - Extracting text from PDF: diseases/STRESS.pdf
2025-06-09 18:33:50,150 - pdf_embedder - INFO - Successfully extracted 2342 characters from diseases/STRESS.pdf
2025-06-09 18:33:50,150 - pdf_embedder - INFO - Created 16 chunks from STRESS.pdf
2025-06-09 18:33:50,150 - pdf_embedder - INFO - Generating embeddings for 16 texts using Ollama
2025-06-09 18:33:50,704 - pdf_embedder - INFO - Generated 16 embeddings total
2025-06-09 18:33:50,704 - pdf_embedder - INFO - Inserting 16 document chunks into database...
2025-06-09 18:33:53,081 - pdf_embedder - INFO - Database connection established successfully on attempt 1
2025-06-09 18:33:55,193 - pdf_embedder - INFO - Successfully inserted 16 document chunks
2025-06-09 18:33:55,193 - pdf_embedder - INFO - Successfully processed and stored 16 chunks for STRESS.pdf
2025-06-09 18:33:55,194 - pdf_embedder - INFO - Processing file 27/34: EPHEMERAL FEVER.pdf
2025-06-09 18:33:55,194 - pdf_embedder - INFO - Processing single PDF: EPHEMERAL FEVER.pdf
2025-06-09 18:33:55,194 - pdf_embedder - INFO - Extracting text from PDF: diseases/EPHEMERAL FEVER.pdf
2025-06-09 18:33:55,211 - pdf_embedder - INFO - Successfully extracted 891 characters from diseases/EPHEMERAL FEVER.pdf
2025-06-09 18:33:55,211 - pdf_embedder - INFO - Created 6 chunks from EPHEMERAL FEVER.pdf
2025-06-09 18:33:55,211 - pdf_embedder - INFO - Generating embeddings for 6 texts using Ollama
2025-06-09 18:33:55,466 - pdf_embedder - INFO - Generated 6 embeddings total
2025-06-09 18:33:55,466 - pdf_embedder - INFO - Inserting 6 document chunks into database...
2025-06-09 18:33:57,817 - pdf_embedder - INFO - Database connection established successfully on attempt 1
2025-06-09 18:33:59,295 - pdf_embedder - INFO - Successfully inserted 6 document chunks
2025-06-09 18:33:59,297 - pdf_embedder - INFO - Successfully processed and stored 6 chunks for EPHEMERAL FEVER.pdf
2025-06-09 18:33:59,297 - pdf_embedder - INFO - Processing file 28/34: Acute Mastitis.pdf
2025-06-09 18:33:59,299 - pdf_embedder - INFO - Processing single PDF: Acute Mastitis.pdf
2025-06-09 18:33:59,300 - pdf_embedder - INFO - Extracting text from PDF: diseases/Acute Mastitis.pdf
2025-06-09 18:33:59,313 - pdf_embedder - INFO - Successfully extracted 525 characters from diseases/Acute Mastitis.pdf
2025-06-09 18:33:59,313 - pdf_embedder - INFO - Created 1 chunks from Acute Mastitis.pdf
2025-06-09 18:33:59,313 - pdf_embedder - INFO - Generating embeddings for 1 texts using Ollama
2025-06-09 18:33:59,422 - pdf_embedder - INFO - Generated 1 embeddings total
2025-06-09 18:33:59,422 - pdf_embedder - INFO - Inserting 1 document chunks into database...
2025-06-09 18:34:01,747 - pdf_embedder - INFO - Database connection established successfully on attempt 1
2025-06-09 18:34:02,607 - pdf_embedder - INFO - Successfully inserted 1 document chunks
2025-06-09 18:34:02,608 - pdf_embedder - INFO - Successfully processed and stored 1 chunks for Acute Mastitis.pdf
2025-06-09 18:34:02,608 - pdf_embedder - INFO - Processing file 29/34: ERGOT POISONING (DEGNALA DISEASE).pdf
2025-06-09 18:34:02,608 - pdf_embedder - INFO - Processing single PDF: ERGOT POISONING (DEGNALA DISEASE).pdf
2025-06-09 18:34:02,609 - pdf_embedder - INFO - Extracting text from PDF: diseases/ERGOT POISONING (DEGNALA DISEASE).pdf
2025-06-09 18:34:02,624 - pdf_embedder - INFO - Successfully extracted 1706 characters from diseases/ERGOT POISONING (DEGNALA DISEASE).pdf
2025-06-09 18:34:02,625 - pdf_embedder - INFO - Created 5 chunks from ERGOT POISONING (DEGNALA DISEASE).pdf
2025-06-09 18:34:02,625 - pdf_embedder - INFO - Generating embeddings for 5 texts using Ollama
2025-06-09 18:34:02,883 - pdf_embedder - INFO - Generated 5 embeddings total
2025-06-09 18:34:02,884 - pdf_embedder - INFO - Inserting 5 document chunks into database...
2025-06-09 18:34:05,138 - pdf_embedder - INFO - Database connection established successfully on attempt 1
2025-06-09 18:34:06,300 - pdf_embedder - INFO - Successfully inserted 5 document chunks
2025-06-09 18:34:06,301 - pdf_embedder - INFO - Successfully processed and stored 5 chunks for ERGOT POISONING (DEGNALA DISEASE).pdf
2025-06-09 18:34:06,302 - pdf_embedder - INFO - Processing file 30/34: ANAPLASMOSIS.pdf
2025-06-09 18:34:06,302 - pdf_embedder - INFO - Processing single PDF: ANAPLASMOSIS.pdf
2025-06-09 18:34:06,302 - pdf_embedder - INFO - Extracting text from PDF: diseases/ANAPLASMOSIS.pdf
2025-06-09 18:34:06,310 - pdf_embedder - INFO - Successfully extracted 511 characters from diseases/ANAPLASMOSIS.pdf
2025-06-09 18:34:06,311 - pdf_embedder - INFO - Created 4 chunks from ANAPLASMOSIS.pdf
2025-06-09 18:34:06,311 - pdf_embedder - INFO - Generating embeddings for 4 texts using Ollama
2025-06-09 18:34:06,472 - pdf_embedder - INFO - Generated 4 embeddings total
2025-06-09 18:34:06,472 - pdf_embedder - INFO - Inserting 4 document chunks into database...
2025-06-09 18:34:08,856 - pdf_embedder - INFO - Database connection established successfully on attempt 1
2025-06-09 18:34:10,048 - pdf_embedder - INFO - Successfully inserted 4 document chunks
2025-06-09 18:34:10,050 - pdf_embedder - INFO - Successfully processed and stored 4 chunks for ANAPLASMOSIS.pdf
2025-06-09 18:34:10,050 - pdf_embedder - INFO - Processing file 31/34: Sub-Acute Mastitis.pdf
2025-06-09 18:34:10,050 - pdf_embedder - INFO - Processing single PDF: Sub-Acute Mastitis.pdf
2025-06-09 18:34:10,050 - pdf_embedder - INFO - Extracting text from PDF: diseases/Sub-Acute Mastitis.pdf
2025-06-09 18:34:10,059 - pdf_embedder - INFO - Successfully extracted 494 characters from diseases/Sub-Acute Mastitis.pdf
2025-06-09 18:34:10,059 - pdf_embedder - INFO - Created 1 chunks from Sub-Acute Mastitis.pdf
2025-06-09 18:34:10,059 - pdf_embedder - INFO - Generating embeddings for 1 texts using Ollama
2025-06-09 18:34:10,140 - pdf_embedder - INFO - Generated 1 embeddings total
2025-06-09 18:34:10,140 - pdf_embedder - INFO - Inserting 1 document chunks into database...
2025-06-09 18:34:12,366 - pdf_embedder - INFO - Database connection established successfully on attempt 1
2025-06-09 18:34:13,205 - pdf_embedder - INFO - Successfully inserted 1 document chunks
2025-06-09 18:34:13,207 - pdf_embedder - INFO - Successfully processed and stored 1 chunks for Sub-Acute Mastitis.pdf
2025-06-09 18:34:13,207 - pdf_embedder - INFO - Processing file 32/34: SKIN INFECTION.pdf
2025-06-09 18:34:13,207 - pdf_embedder - INFO - Processing single PDF: SKIN INFECTION.pdf
2025-06-09 18:34:13,207 - pdf_embedder - INFO - Extracting text from PDF: diseases/SKIN INFECTION.pdf
2025-06-09 18:34:13,216 - pdf_embedder - INFO - Successfully extracted 787 characters from diseases/SKIN INFECTION.pdf
2025-06-09 18:34:13,216 - pdf_embedder - INFO - Created 5 chunks from SKIN INFECTION.pdf
2025-06-09 18:34:13,217 - pdf_embedder - INFO - Generating embeddings for 5 texts using Ollama
2025-06-09 18:34:13,419 - pdf_embedder - INFO - Generated 5 embeddings total
2025-06-09 18:34:13,419 - pdf_embedder - INFO - Inserting 5 document chunks into database...
2025-06-09 18:34:15,783 - pdf_embedder - INFO - Database connection established successfully on attempt 1
2025-06-09 18:34:16,963 - pdf_embedder - INFO - Successfully inserted 5 document chunks
2025-06-09 18:34:16,963 - pdf_embedder - INFO - Successfully processed and stored 5 chunks for SKIN INFECTION.pdf
2025-06-09 18:34:16,964 - pdf_embedder - INFO - Processing file 33/34: COLIC PAIN IN ADULT.pdf
2025-06-09 18:34:16,964 - pdf_embedder - INFO - Processing single PDF: COLIC PAIN IN ADULT.pdf
2025-06-09 18:34:16,964 - pdf_embedder - INFO - Extracting text from PDF: diseases/COLIC PAIN IN ADULT.pdf
2025-06-09 18:34:16,977 - pdf_embedder - INFO - Successfully extracted 1656 characters from diseases/COLIC PAIN IN ADULT.pdf
2025-06-09 18:34:16,978 - pdf_embedder - INFO - Created 14 chunks from COLIC PAIN IN ADULT.pdf
2025-06-09 18:34:16,978 - pdf_embedder - INFO - Generating embeddings for 14 texts using Ollama
2025-06-09 18:34:17,391 - pdf_embedder - INFO - Generated 14 embeddings total
2025-06-09 18:34:17,392 - pdf_embedder - INFO - Inserting 14 document chunks into database...
2025-06-09 18:34:19,674 - pdf_embedder - INFO - Database connection established successfully on attempt 1
2025-06-09 18:34:21,733 - pdf_embedder - INFO - Successfully inserted 14 document chunks
2025-06-09 18:34:21,734 - pdf_embedder - INFO - Successfully processed and stored 14 chunks for COLIC PAIN IN ADULT.pdf
2025-06-09 18:34:21,734 - pdf_embedder - INFO - Processing file 34/34: IMPACTION OF RUMEN.pdf
2025-06-09 18:34:21,734 - pdf_embedder - INFO - Processing single PDF: IMPACTION OF RUMEN.pdf
2025-06-09 18:34:21,734 - pdf_embedder - INFO - Extracting text from PDF: diseases/IMPACTION OF RUMEN.pdf
2025-06-09 18:34:21,741 - pdf_embedder - INFO - Successfully extracted 838 characters from diseases/IMPACTION OF RUMEN.pdf
2025-06-09 18:34:21,742 - pdf_embedder - INFO - Created 6 chunks from IMPACTION OF RUMEN.pdf
2025-06-09 18:34:21,742 - pdf_embedder - INFO - Generating embeddings for 6 texts using Ollama
2025-06-09 18:34:21,966 - pdf_embedder - INFO - Generated 6 embeddings total
2025-06-09 18:34:21,967 - pdf_embedder - INFO - Inserting 6 document chunks into database...
2025-06-09 18:34:24,348 - pdf_embedder - INFO - Database connection established successfully on attempt 1
2025-06-09 18:34:25,838 - pdf_embedder - INFO - Successfully inserted 6 document chunks
2025-06-09 18:34:25,839 - pdf_embedder - INFO - Successfully processed and stored 6 chunks for IMPACTION OF RUMEN.pdf
2025-06-09 18:34:28,213 - pdf_embedder - INFO - Database connection established successfully on attempt 1
2025-06-09 18:34:28,798 - pdf_embedder - INFO - Processing complete! Total documents in database: 201
2025-06-09 18:37:12,785 - __main__ - INFO - 🚀 Starting Medical Document Search System API...
2025-06-09 18:37:12,785 - __main__ - INFO - 🔄 Initializing systems (attempt 1/3)...
2025-06-09 18:37:12,785 - __main__ - INFO - Initializing Flask app systems...
2025-06-09 18:37:12,785 - __main__ - INFO - Using embedder type: nomic
2025-06-09 18:37:12,785 - search_system - INFO - Initializing MedicalSearchSystem with embedder type: nomic
2025-06-09 18:37:12,785 - pdf_embedder - INFO - Initialized NomicEmbedder with Ollama - base_url: http://localhost:11434, model: nomic-embed-text:latest
2025-06-09 18:37:12,785 - pdf_embedder - INFO - Using DATABASE_URL for database connection (production mode)
2025-06-09 18:37:12,785 - pdf_embedder - INFO - Using table name: documents
2025-06-09 18:37:12,785 - search_system - INFO - Initialized VectorSearchEngine with nomic embedder and PostgreSQLManager
2025-06-09 18:37:12,785 - search_system - INFO - Initialized GroqLLM with model: llama-3.3-70b-versatile
2025-06-09 18:37:12,785 - search_system - INFO - MedicalSearchSystem initialized successfully
2025-06-09 18:37:12,785 - pdf_embedder - INFO - Initializing PDFEmbeddingPipeline with embedder type: nomic
2025-06-09 18:37:12,785 - pdf_embedder - INFO - Initialized NomicEmbedder with Ollama - base_url: http://localhost:11434, model: nomic-embed-text:latest
2025-06-09 18:37:12,785 - pdf_embedder - INFO - Initialized PDFProcessor with chunk_size: 1000, chunk_overlap: 200
2025-06-09 18:37:12,785 - pdf_embedder - INFO - Using DATABASE_URL for database connection (production mode)
2025-06-09 18:37:12,785 - pdf_embedder - INFO - Using table name: documents
2025-06-09 18:37:12,786 - pdf_embedder - INFO - PDFEmbeddingPipeline initialized successfully
2025-06-09 18:37:12,786 - __main__ - INFO - Flask app systems initialized successfully
2025-06-09 18:37:12,786 - __main__ - INFO - ✅ Systems initialized successfully
2025-06-09 18:37:12,786 - __main__ - INFO - 🌐 Starting Flask server on http://0.0.0.0:5001
2025-06-09 18:37:12,786 - __main__ - INFO - 📚 Web interface available at http://0.0.0.0:5001
2025-06-09 18:37:12,786 - __main__ - INFO - 🔍 API endpoints available at http://0.0.0.0:5001/api/
2025-06-09 18:37:12,786 - __main__ - INFO - 🌍 Environment: development
2025-06-09 18:37:12,786 - __main__ - INFO - 🐛 Debug mode: False
2025-06-09 18:37:12,797 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://***********:5001
2025-06-09 18:37:12,797 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-09 18:37:33,097 - __main__ - INFO - Search endpoint called
2025-06-09 18:37:33,099 - __main__ - INFO - Search request - Query: 'Rumen motility reduced...', top_k: 10
2025-06-09 18:37:35,530 - pdf_embedder - INFO - Database connection established successfully on attempt 1
2025-06-09 18:37:36,346 - search_system - INFO - Database stats: 201 documents from 34 files
2025-06-09 18:37:36,625 - __main__ - INFO - Database has 201 documents, proceeding with search
2025-06-09 18:37:36,626 - search_system - INFO - Starting search and answer for query: 'Rumen motility reduced...' (top_k=10)
2025-06-09 18:37:36,626 - search_system - INFO - Starting vector search for query: 'Rumen motility reduced...' (top_k=10)
2025-06-09 18:37:36,627 - pdf_embedder - INFO - Generating embeddings for 1 texts using Ollama
2025-06-09 18:37:36,889 - pdf_embedder - INFO - Generated 1 embeddings total
2025-06-09 18:37:39,158 - pdf_embedder - INFO - Database connection established successfully on attempt 1
2025-06-09 18:37:40,021 - search_system - INFO - Found 2 raw results from database
2025-06-09 18:37:40,022 - search_system - INFO - Successfully processed 2 documents from search results
2025-06-09 18:37:40,304 - search_system - INFO - Found 2 relevant documents
2025-06-09 18:37:40,304 - search_system - INFO - Generating response with Groq LLM...
2025-06-09 18:37:40,304 - search_system - INFO - Generating LLM response for query: 'Rumen motility reduced...'
2025-06-09 18:37:42,090 - search_system - INFO - Successfully generated LLM response (2039 characters)
2025-06-09 18:37:42,091 - search_system - INFO - Search and answer completed successfully - found 2 documents, generated 2039 character response
2025-06-09 18:37:42,091 - __main__ - INFO - Search completed successfully - found 2 documents, generated 2039 character response
2025-06-09 18:37:42,094 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 18:37:42] "POST /api/search HTTP/1.1" 200 -
2025-06-09 18:38:56,124 - __main__ - INFO - Search endpoint called
2025-06-09 18:38:56,127 - __main__ - INFO - Search request - Query: 'Intestinal obstruction...', top_k: 10
2025-06-09 18:38:58,607 - pdf_embedder - INFO - Database connection established successfully on attempt 1
2025-06-09 18:38:59,443 - search_system - INFO - Database stats: 201 documents from 34 files
2025-06-09 18:38:59,712 - __main__ - INFO - Database has 201 documents, proceeding with search
2025-06-09 18:38:59,713 - search_system - INFO - Starting search and answer for query: 'Intestinal obstruction...' (top_k=10)
2025-06-09 18:38:59,713 - search_system - INFO - Starting vector search for query: 'Intestinal obstruction...' (top_k=10)
2025-06-09 18:38:59,713 - pdf_embedder - INFO - Generating embeddings for 1 texts using Ollama
2025-06-09 18:38:59,993 - pdf_embedder - INFO - Generated 1 embeddings total
2025-06-09 18:39:02,325 - pdf_embedder - INFO - Database connection established successfully on attempt 1
2025-06-09 18:39:03,229 - search_system - INFO - Found 2 raw results from database
2025-06-09 18:39:03,231 - search_system - INFO - Successfully processed 2 documents from search results
2025-06-09 18:39:03,520 - search_system - INFO - Found 2 relevant documents
2025-06-09 18:39:03,521 - search_system - INFO - Generating response with Groq LLM...
2025-06-09 18:39:03,521 - search_system - INFO - Generating LLM response for query: 'Intestinal obstruction...'
2025-06-09 18:39:05,213 - search_system - INFO - Successfully generated LLM response (2188 characters)
2025-06-09 18:39:05,214 - search_system - INFO - Search and answer completed successfully - found 2 documents, generated 2188 character response
2025-06-09 18:39:05,214 - __main__ - INFO - Search completed successfully - found 2 documents, generated 2188 character response
2025-06-09 18:39:05,217 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 18:39:05] "POST /api/search HTTP/1.1" 200 -
2025-06-09 18:39:38,746 - __main__ - INFO - Search endpoint called
2025-06-09 18:39:38,752 - __main__ - INFO - Search request - Query: 'fever...', top_k: 10
2025-06-09 18:39:41,182 - pdf_embedder - INFO - Database connection established successfully on attempt 1
2025-06-09 18:39:42,058 - search_system - INFO - Database stats: 201 documents from 34 files
2025-06-09 18:39:42,347 - __main__ - INFO - Database has 201 documents, proceeding with search
2025-06-09 18:39:42,347 - search_system - INFO - Starting search and answer for query: 'fever...' (top_k=10)
2025-06-09 18:39:42,347 - search_system - INFO - Starting vector search for query: 'fever...' (top_k=10)
2025-06-09 18:39:42,348 - pdf_embedder - INFO - Generating embeddings for 1 texts using Ollama
2025-06-09 18:39:42,625 - pdf_embedder - INFO - Generated 1 embeddings total
2025-06-09 18:39:44,977 - pdf_embedder - INFO - Database connection established successfully on attempt 1
2025-06-09 18:39:45,852 - search_system - INFO - Found 10 raw results from database
2025-06-09 18:39:45,854 - search_system - INFO - Successfully processed 10 documents from search results
2025-06-09 18:39:46,137 - search_system - INFO - Found 10 relevant documents
2025-06-09 18:39:46,137 - search_system - INFO - Generating response with Groq LLM...
2025-06-09 18:39:46,137 - search_system - INFO - Generating LLM response for query: 'fever...'
2025-06-09 18:39:48,212 - search_system - INFO - Successfully generated LLM response (2385 characters)
2025-06-09 18:39:48,213 - search_system - INFO - Search and answer completed successfully - found 10 documents, generated 2385 character response
2025-06-09 18:39:48,214 - __main__ - INFO - Search completed successfully - found 10 documents, generated 2385 character response
2025-06-09 18:39:48,219 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 18:39:48] "POST /api/search HTTP/1.1" 200 -
2025-06-09 18:40:29,822 - __main__ - INFO - Search endpoint called
2025-06-09 18:40:29,824 - __main__ - INFO - Search request - Query: 'There is peculiar symptom such as while standing lowering of back & hind leg kicking on backward gro...', top_k: 10
2025-06-09 18:40:32,308 - pdf_embedder - INFO - Database connection established successfully on attempt 1
2025-06-09 18:40:33,164 - search_system - INFO - Database stats: 201 documents from 34 files
2025-06-09 18:40:33,454 - __main__ - INFO - Database has 201 documents, proceeding with search
2025-06-09 18:40:33,454 - search_system - INFO - Starting search and answer for query: 'There is peculiar symptom such as while standing lowering of back & hind leg kicking on backward gro...' (top_k=10)
2025-06-09 18:40:33,454 - search_system - INFO - Starting vector search for query: 'There is peculiar symptom such as while standing lowering of back & hind leg kicking on backward gro...' (top_k=10)
2025-06-09 18:40:33,455 - pdf_embedder - INFO - Generating embeddings for 1 texts using Ollama
2025-06-09 18:40:33,724 - pdf_embedder - INFO - Generated 1 embeddings total
2025-06-09 18:40:35,991 - pdf_embedder - INFO - Database connection established successfully on attempt 1
2025-06-09 18:40:36,862 - search_system - INFO - Found 10 raw results from database
2025-06-09 18:40:36,864 - search_system - INFO - Successfully processed 10 documents from search results
2025-06-09 18:40:37,138 - search_system - INFO - Found 10 relevant documents
2025-06-09 18:40:37,139 - search_system - INFO - Generating response with Groq LLM...
2025-06-09 18:40:37,139 - search_system - INFO - Generating LLM response for query: 'There is peculiar symptom such as while standing lowering of back & hind leg kicking on backward gro...'
2025-06-09 18:40:39,168 - search_system - INFO - Successfully generated LLM response (2121 characters)
2025-06-09 18:40:39,169 - search_system - INFO - Search and answer completed successfully - found 10 documents, generated 2121 character response
2025-06-09 18:40:39,169 - __main__ - INFO - Search completed successfully - found 10 documents, generated 2121 character response
2025-06-09 18:40:39,171 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 18:40:39] "POST /api/search HTTP/1.1" 200 -
2025-06-09 19:04:10,509 - __main__ - INFO - Search endpoint called
2025-06-09 19:04:10,511 - __main__ - INFO - Search request - Query: 'There is peculiar symptom such as while standing lowering of back & hind leg kicking on backward gro...', top_k: 10
2025-06-09 19:04:13,044 - pdf_embedder - INFO - Database connection established successfully on attempt 1
2025-06-09 19:04:13,899 - search_system - INFO - Database stats: 201 documents from 34 files
2025-06-09 19:04:14,181 - __main__ - INFO - Database has 201 documents, proceeding with search
2025-06-09 19:04:14,182 - search_system - INFO - Starting search and answer for query: 'There is peculiar symptom such as while standing lowering of back & hind leg kicking on backward gro...' (top_k=10)
2025-06-09 19:04:14,182 - search_system - INFO - Starting vector search for query: 'There is peculiar symptom such as while standing lowering of back & hind leg kicking on backward gro...' (top_k=10)
2025-06-09 19:04:14,182 - pdf_embedder - INFO - Generating embeddings for 1 texts using Ollama
2025-06-09 19:04:14,853 - pdf_embedder - INFO - Generated 1 embeddings total
2025-06-09 19:04:17,568 - pdf_embedder - INFO - Database connection established successfully on attempt 1
2025-06-09 19:04:18,459 - search_system - INFO - Found 10 raw results from database
2025-06-09 19:04:18,463 - search_system - INFO - Successfully processed 10 documents from search results
2025-06-09 19:04:18,757 - search_system - INFO - Found 10 relevant documents
2025-06-09 19:04:18,758 - search_system - INFO - Generating response with Groq LLM...
2025-06-09 19:04:18,758 - search_system - INFO - Generating LLM response for query: 'There is peculiar symptom such as while standing lowering of back & hind leg kicking on backward gro...'
2025-06-09 19:04:20,630 - search_system - INFO - Successfully generated LLM response (2095 characters)
2025-06-09 19:04:20,631 - search_system - INFO - Search and answer completed successfully - found 10 documents, generated 2095 character response
2025-06-09 19:04:20,632 - __main__ - INFO - Search completed successfully - found 10 documents, generated 2095 character response
2025-06-09 19:04:20,635 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:04:20] "POST /api/search HTTP/1.1" 200 -
2025-06-09 19:17:11,980 - __main__ - INFO - Search endpoint called
2025-06-09 19:17:11,982 - __main__ - INFO - Search request - Query: 'There is peculiar symptom such as while standing lowering of back & hind leg kicking on backward gro...', top_k: 10
2025-06-09 19:17:14,558 - pdf_embedder - INFO - Database connection established successfully on attempt 1
2025-06-09 19:17:15,433 - search_system - INFO - Database stats: 201 documents from 34 files
2025-06-09 19:17:15,740 - __main__ - INFO - Database has 201 documents, proceeding with search
2025-06-09 19:17:15,740 - search_system - INFO - Starting search and answer for query: 'There is peculiar symptom such as while standing lowering of back & hind leg kicking on backward gro...' (top_k=10)
2025-06-09 19:17:15,741 - search_system - INFO - Starting vector search for query: 'There is peculiar symptom such as while standing lowering of back & hind leg kicking on backward gro...' (top_k=10)
2025-06-09 19:17:15,741 - pdf_embedder - INFO - Generating embeddings for 1 texts using Ollama
2025-06-09 19:17:16,397 - pdf_embedder - INFO - Generated 1 embeddings total
2025-06-09 19:17:18,705 - pdf_embedder - INFO - Database connection established successfully on attempt 1
2025-06-09 19:17:19,571 - search_system - INFO - Found 10 raw results from database
2025-06-09 19:17:19,574 - search_system - INFO - Successfully processed 10 documents from search results
2025-06-09 19:17:19,868 - search_system - INFO - Found 10 relevant documents
2025-06-09 19:17:19,869 - search_system - INFO - Generating response with Groq LLM...
2025-06-09 19:17:19,869 - search_system - INFO - Generating LLM response for query: 'There is peculiar symptom such as while standing lowering of back & hind leg kicking on backward gro...'
2025-06-09 19:17:21,707 - search_system - INFO - Successfully generated LLM response (2063 characters)
2025-06-09 19:17:21,709 - search_system - INFO - Search and answer completed successfully - found 10 documents, generated 2063 character response
2025-06-09 19:17:21,709 - __main__ - INFO - Search completed successfully - found 10 documents, generated 2063 character response
2025-06-09 19:17:21,711 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:17:21] "POST /api/search HTTP/1.1" 200 -
2025-06-09 19:18:05,006 - __main__ - INFO - 🚀 Starting Medical Document Search System API...
2025-06-09 19:18:05,007 - __main__ - INFO - 🔄 Initializing systems (attempt 1/3)...
2025-06-09 19:18:05,007 - __main__ - INFO - Initializing Flask app systems...
2025-06-09 19:18:05,007 - __main__ - INFO - Using embedder type: huggingface
2025-06-09 19:18:05,007 - search_system - INFO - Initializing MedicalSearchSystem with embedder type: huggingface
2025-06-09 19:18:05,007 - pdf_embedder - INFO - Initialized HuggingFaceEmbedder with model: nomic-ai/nomic-embed-text-v1
2025-06-09 19:18:05,007 - pdf_embedder - INFO - Using DATABASE_URL for database connection (production mode)
2025-06-09 19:18:05,007 - pdf_embedder - INFO - Using table name: documents
2025-06-09 19:18:05,007 - search_system - INFO - Initialized VectorSearchEngine with huggingface embedder and PostgreSQLManager
2025-06-09 19:18:05,008 - search_system - INFO - Initialized GroqLLM with model: llama-3.3-70b-versatile
2025-06-09 19:18:05,008 - search_system - INFO - MedicalSearchSystem initialized successfully
2025-06-09 19:18:05,008 - pdf_embedder - INFO - Initializing PDFEmbeddingPipeline with embedder type: huggingface
2025-06-09 19:18:05,008 - pdf_embedder - INFO - Initialized HuggingFaceEmbedder with model: nomic-ai/nomic-embed-text-v1
2025-06-09 19:18:05,008 - pdf_embedder - INFO - Initialized PDFProcessor with chunk_size: 1000, chunk_overlap: 200
2025-06-09 19:18:05,008 - pdf_embedder - INFO - Using DATABASE_URL for database connection (production mode)
2025-06-09 19:18:05,008 - pdf_embedder - INFO - Using table name: documents
2025-06-09 19:18:05,008 - pdf_embedder - INFO - PDFEmbeddingPipeline initialized successfully
2025-06-09 19:18:05,008 - __main__ - INFO - Flask app systems initialized successfully
2025-06-09 19:18:05,008 - __main__ - INFO - ✅ Systems initialized successfully
2025-06-09 19:18:05,009 - __main__ - INFO - 🌐 Starting Flask server on http://0.0.0.0:5001
2025-06-09 19:18:05,009 - __main__ - INFO - 📚 Web interface available at http://0.0.0.0:5001
2025-06-09 19:18:05,009 - __main__ - INFO - 🔍 API endpoints available at http://0.0.0.0:5001/api/
2025-06-09 19:18:05,009 - __main__ - INFO - 🌍 Environment: development
2025-06-09 19:18:05,009 - __main__ - INFO - 🐛 Debug mode: False
2025-06-09 19:18:05,157 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://***********:5001
2025-06-09 19:18:05,157 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-09 19:18:17,377 - __main__ - INFO - Search endpoint called
2025-06-09 19:18:17,378 - __main__ - INFO - Search request - Query: 'There is peculiar symptom such as while standing lowering of back & hind leg kicking on backward gro...', top_k: 10
2025-06-09 19:18:19,782 - pdf_embedder - INFO - Database connection established successfully on attempt 1
2025-06-09 19:18:20,631 - search_system - INFO - Database stats: 201 documents from 34 files
2025-06-09 19:18:20,908 - __main__ - INFO - Database has 201 documents, proceeding with search
2025-06-09 19:18:20,909 - search_system - INFO - Starting search and answer for query: 'There is peculiar symptom such as while standing lowering of back & hind leg kicking on backward gro...' (top_k=10)
2025-06-09 19:18:20,909 - search_system - INFO - Starting vector search for query: 'There is peculiar symptom such as while standing lowering of back & hind leg kicking on backward gro...' (top_k=10)
2025-06-09 19:18:20,909 - pdf_embedder - INFO - Generating embeddings for 1 texts using Hugging Face
2025-06-09 19:18:21,452 - pdf_embedder - ERROR - HF API error for text 0: 404 - Not Found
2025-06-09 19:18:21,454 - pdf_embedder - INFO - Generated 1 embeddings using Hugging Face
2025-06-09 19:18:23,790 - pdf_embedder - INFO - Database connection established successfully on attempt 1
2025-06-09 19:18:24,692 - search_system - ERROR - Error searching documents: different vector dimensions 768 and 384
Traceback (most recent call last):
  File "/Users/<USER>/krushal/ragV2/search_system.py", line 184, in search_similar_documents
    cur.execute(search_sql, (query_embedding, query_embedding, query_embedding, top_k))
psycopg2.errors.DataException: different vector dimensions 768 and 384

2025-06-09 19:18:24,694 - search_system - WARNING - No relevant documents found for query
2025-06-09 19:18:24,695 - __main__ - INFO - Search completed successfully - found 0 documents, generated 43 character response
2025-06-09 19:18:24,698 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:18:24] "POST /api/search HTTP/1.1" 200 -
2025-06-09 19:27:48,070 - __main__ - INFO - 🚀 Starting Medical Document Search System API...
2025-06-09 19:27:48,070 - __main__ - INFO - 🔄 Initializing systems (attempt 1/3)...
2025-06-09 19:27:48,070 - __main__ - INFO - Initializing Flask app systems...
2025-06-09 19:27:48,070 - __main__ - INFO - Using embedder type: huggingface
2025-06-09 19:27:48,070 - search_system - INFO - Initializing MedicalSearchSystem with embedder type: huggingface
2025-06-09 19:27:48,070 - pdf_embedder - INFO - Initialized HuggingFaceEmbedder with model: sentence-transformers/all-MiniLM-L6-v2
2025-06-09 19:27:48,070 - pdf_embedder - INFO - Using API URL: https://api-inference.huggingface.co/models/sentence-transformers/all-MiniLM-L6-v2
2025-06-09 19:27:48,070 - pdf_embedder - INFO - Using DATABASE_URL for database connection (production mode)
2025-06-09 19:27:48,070 - pdf_embedder - INFO - Using table name: documents
2025-06-09 19:27:48,070 - search_system - INFO - Initialized VectorSearchEngine with huggingface embedder and PostgreSQLManager
2025-06-09 19:27:48,070 - search_system - INFO - Initialized GroqLLM with model: llama-3.3-70b-versatile
2025-06-09 19:27:48,070 - search_system - INFO - MedicalSearchSystem initialized successfully
2025-06-09 19:27:48,070 - pdf_embedder - INFO - Initializing PDFEmbeddingPipeline with embedder type: huggingface
2025-06-09 19:27:48,071 - pdf_embedder - INFO - Initialized HuggingFaceEmbedder with model: sentence-transformers/all-MiniLM-L6-v2
2025-06-09 19:27:48,071 - pdf_embedder - INFO - Using API URL: https://api-inference.huggingface.co/models/sentence-transformers/all-MiniLM-L6-v2
2025-06-09 19:27:48,071 - pdf_embedder - INFO - Initialized PDFProcessor with chunk_size: 1000, chunk_overlap: 200
2025-06-09 19:27:48,071 - pdf_embedder - INFO - Using DATABASE_URL for database connection (production mode)
2025-06-09 19:27:48,071 - pdf_embedder - INFO - Using table name: documents
2025-06-09 19:27:48,071 - pdf_embedder - INFO - PDFEmbeddingPipeline initialized successfully
2025-06-09 19:27:48,071 - __main__ - INFO - Flask app systems initialized successfully
2025-06-09 19:27:48,071 - __main__ - INFO - ✅ Systems initialized successfully
2025-06-09 19:27:48,071 - __main__ - INFO - 🌐 Starting Flask server on http://0.0.0.0:5001
2025-06-09 19:27:48,071 - __main__ - INFO - 📚 Web interface available at http://0.0.0.0:5001
2025-06-09 19:27:48,071 - __main__ - INFO - 🔍 API endpoints available at http://0.0.0.0:5001/api/
2025-06-09 19:27:48,071 - __main__ - INFO - 🌍 Environment: development
2025-06-09 19:27:48,071 - __main__ - INFO - 🐛 Debug mode: False
2025-06-09 19:27:55,230 - __main__ - INFO - 🚀 Starting Medical Document Search System API...
2025-06-09 19:27:55,230 - __main__ - INFO - 🔄 Initializing systems (attempt 1/3)...
2025-06-09 19:27:55,230 - __main__ - INFO - Initializing Flask app systems...
2025-06-09 19:27:55,230 - __main__ - INFO - Using embedder type: sentence_transformers
2025-06-09 19:27:55,230 - search_system - INFO - Initializing MedicalSearchSystem with embedder type: sentence_transformers
2025-06-09 19:27:55,230 - pdf_embedder - INFO - Initialized SentenceTransformersEmbedder with model: nomic-ai/nomic-embed-text-v1
2025-06-09 19:27:58,805 - pdf_embedder - INFO - Loading Sentence Transformers model: nomic-ai/nomic-embed-text-v1
2025-06-09 19:27:58,858 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: mps
2025-06-09 19:27:58,859 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: nomic-ai/nomic-embed-text-v1
2025-06-09 19:28:02,355 - pdf_embedder - ERROR - Error loading Sentence Transformers model: Loading nomic-ai/nomic-embed-text-v1 requires you to execute the configuration file in that repo on your local machine. Make sure you have read the code there to avoid malicious use, then set the option `trust_remote_code=True` to remove this error.
2025-06-09 19:28:02,355 - pdf_embedder - INFO - Using DATABASE_URL for database connection (production mode)
2025-06-09 19:28:02,356 - pdf_embedder - INFO - Using table name: documents
2025-06-09 19:28:02,356 - search_system - INFO - Initialized VectorSearchEngine with sentence_transformers embedder and PostgreSQLManager
2025-06-09 19:28:02,356 - search_system - INFO - Initialized GroqLLM with model: llama-3.3-70b-versatile
2025-06-09 19:28:02,356 - search_system - INFO - MedicalSearchSystem initialized successfully
2025-06-09 19:28:02,356 - pdf_embedder - INFO - Initializing PDFEmbeddingPipeline with embedder type: sentence_transformers
2025-06-09 19:28:02,356 - pdf_embedder - INFO - Initialized SentenceTransformersEmbedder with model: nomic-ai/nomic-embed-text-v1
2025-06-09 19:28:02,356 - pdf_embedder - INFO - Loading Sentence Transformers model: nomic-ai/nomic-embed-text-v1
2025-06-09 19:28:02,361 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: mps
2025-06-09 19:28:02,361 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: nomic-ai/nomic-embed-text-v1
2025-06-09 19:28:04,253 - pdf_embedder - ERROR - Error loading Sentence Transformers model: Loading nomic-ai/nomic-embed-text-v1 requires you to execute the configuration file in that repo on your local machine. Make sure you have read the code there to avoid malicious use, then set the option `trust_remote_code=True` to remove this error.
2025-06-09 19:28:04,253 - pdf_embedder - INFO - Initialized PDFProcessor with chunk_size: 1000, chunk_overlap: 200
2025-06-09 19:28:04,253 - pdf_embedder - INFO - Using DATABASE_URL for database connection (production mode)
2025-06-09 19:28:04,253 - pdf_embedder - INFO - Using table name: documents
2025-06-09 19:28:04,253 - pdf_embedder - INFO - PDFEmbeddingPipeline initialized successfully
2025-06-09 19:28:04,253 - __main__ - INFO - Flask app systems initialized successfully
2025-06-09 19:28:04,253 - __main__ - INFO - ✅ Systems initialized successfully
2025-06-09 19:28:04,253 - __main__ - INFO - 🌐 Starting Flask server on http://0.0.0.0:5001
2025-06-09 19:28:04,253 - __main__ - INFO - 📚 Web interface available at http://0.0.0.0:5001
2025-06-09 19:28:04,253 - __main__ - INFO - 🔍 API endpoints available at http://0.0.0.0:5001/api/
2025-06-09 19:28:04,253 - __main__ - INFO - 🌍 Environment: development
2025-06-09 19:28:04,253 - __main__ - INFO - 🐛 Debug mode: False
2025-06-09 19:28:04,260 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://***********:5001
2025-06-09 19:28:04,260 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-09 19:28:57,996 - __main__ - INFO - Search endpoint called
2025-06-09 19:28:57,997 - __main__ - INFO - Search request - Query: 'There is peculiar symptom such as while standing lowering of back & hind leg kicking on backward gro...', top_k: 10
2025-06-09 19:29:00,499 - pdf_embedder - INFO - Database connection established successfully on attempt 1
2025-06-09 19:29:01,362 - search_system - INFO - Database stats: 201 documents from 34 files
2025-06-09 19:29:01,641 - __main__ - INFO - Database has 201 documents, proceeding with search
2025-06-09 19:29:01,642 - search_system - INFO - Starting search and answer for query: 'There is peculiar symptom such as while standing lowering of back & hind leg kicking on backward gro...' (top_k=10)
2025-06-09 19:29:01,642 - search_system - INFO - Starting vector search for query: 'There is peculiar symptom such as while standing lowering of back & hind leg kicking on backward gro...' (top_k=10)
2025-06-09 19:29:01,642 - pdf_embedder - INFO - Generating embeddings for 1 texts using Sentence Transformers
2025-06-09 19:29:01,642 - pdf_embedder - ERROR - Sentence Transformers model not available, returning random embeddings
2025-06-09 19:29:04,009 - pdf_embedder - INFO - Database connection established successfully on attempt 1
2025-06-09 19:29:04,874 - search_system - ERROR - Error searching documents: different vector dimensions 768 and 384
Traceback (most recent call last):
  File "/Users/<USER>/krushal/ragV2/search_system.py", line 186, in search_similar_documents
    cur.execute(search_sql, (query_embedding, query_embedding, query_embedding, top_k))
psycopg2.errors.DataException: different vector dimensions 768 and 384

2025-06-09 19:29:04,877 - search_system - WARNING - No relevant documents found for query
2025-06-09 19:29:04,877 - __main__ - INFO - Search completed successfully - found 0 documents, generated 43 character response
2025-06-09 19:29:04,880 - werkzeug - INFO - 127.0.0.1 - - [09/Jun/2025 19:29:04] "POST /api/search HTTP/1.1" 200 -
2025-06-09 19:33:58,760 - search_system - INFO - Initializing MedicalSearchSystem with embedder type: nomic
2025-06-09 19:33:58,760 - pdf_embedder - INFO - Initialized NomicEmbedder with Ollama - base_url: http://localhost:11434, model: nomic-embed-text:latest
2025-06-09 19:33:58,760 - pdf_embedder - INFO - Using DATABASE_URL for database connection (production mode)
2025-06-09 19:33:58,760 - pdf_embedder - INFO - Using table name: documents
2025-06-09 19:33:58,760 - search_system - INFO - Initialized VectorSearchEngine with nomic embedder and PostgreSQLManager
2025-06-09 19:33:58,760 - search_system - INFO - Initialized GroqLLM with model: llama-3.3-70b-versatile
2025-06-09 19:33:58,761 - search_system - INFO - MedicalSearchSystem initialized successfully
2025-06-09 19:33:58,761 - search_system - INFO - Starting search and answer for query: 'test query...' (top_k=1)
2025-06-09 19:33:58,761 - search_system - INFO - Starting vector search for query: 'test query...' (top_k=1)
2025-06-09 19:33:58,761 - pdf_embedder - INFO - Generating embeddings for 1 texts using Ollama
2025-06-09 19:33:59,391 - pdf_embedder - INFO - Generated 1 embeddings total
2025-06-09 19:34:01,989 - pdf_embedder - INFO - Database connection established successfully on attempt 1
2025-06-09 19:34:02,845 - search_system - INFO - Found 1 raw results from database
2025-06-09 19:34:02,846 - search_system - INFO - Successfully processed 1 documents from search results
2025-06-09 19:34:03,133 - search_system - INFO - Found 1 relevant documents
2025-06-09 19:34:03,133 - search_system - INFO - Generating response with Groq LLM...
2025-06-09 19:34:03,133 - search_system - INFO - Generating LLM response for query: 'test query...'
2025-06-09 19:34:11,948 - search_system - INFO - Successfully generated LLM response (2205 characters)
2025-06-09 19:34:11,951 - search_system - INFO - Search and answer completed successfully - found 1 documents, generated 2205 character response
2025-06-09 19:35:31,187 - pdf_embedder - INFO - Initializing PDFEmbeddingPipeline with embedder type: nomic
2025-06-09 19:35:31,187 - pdf_embedder - INFO - Initialized NomicEmbedder with Ollama - base_url: http://localhost:11434, model: nomic-embed-text:latest
2025-06-09 19:35:31,187 - pdf_embedder - INFO - Initialized PDFProcessor with chunk_size: 1000, chunk_overlap: 200
2025-06-09 19:35:31,187 - pdf_embedder - INFO - Using DATABASE_URL for database connection (production mode)
2025-06-09 19:35:31,187 - pdf_embedder - INFO - Using table name: documents
2025-06-09 19:35:31,187 - pdf_embedder - INFO - PDFEmbeddingPipeline initialized successfully
2025-06-09 19:35:31,187 - pdf_embedder - INFO - Starting to process PDF folder: diseases
2025-06-09 19:35:31,187 - pdf_embedder - INFO - Testing embedding dimension...
2025-06-09 19:35:31,187 - pdf_embedder - INFO - Generating embeddings for 1 texts using Ollama
2025-06-09 19:35:31,392 - pdf_embedder - INFO - Generated 1 embeddings total
2025-06-09 19:35:31,392 - pdf_embedder - INFO - Embedder produces 768-dimensional vectors
2025-06-09 19:35:33,880 - pdf_embedder - INFO - Database connection established successfully on attempt 1
2025-06-09 19:35:34,176 - pdf_embedder - INFO - Table doesn't exist yet
2025-06-09 19:35:34,475 - pdf_embedder - INFO - No existing table, dimension check passed
2025-06-09 19:35:34,475 - pdf_embedder - INFO - Creating tables for documents with 768 dimensions...
2025-06-09 19:35:36,838 - pdf_embedder - INFO - Database connection established successfully on attempt 1
2025-06-09 19:35:37,478 - pdf_embedder - INFO - Tables created successfully with 768 dimensions!
2025-06-09 19:35:37,482 - pdf_embedder - INFO - Found 34 PDF files in diseases
2025-06-09 19:35:37,482 - pdf_embedder - INFO - Processing file 1/34: DYSENTERY.pdf
2025-06-09 19:35:37,482 - pdf_embedder - INFO - Processing single PDF: DYSENTERY.pdf
2025-06-09 19:35:37,482 - pdf_embedder - INFO - Extracting text from PDF: diseases/DYSENTERY.pdf
2025-06-09 19:35:37,509 - pdf_embedder - INFO - Successfully extracted 815 characters from diseases/DYSENTERY.pdf
2025-06-09 19:35:37,509 - pdf_embedder - INFO - Created 5 chunks from DYSENTERY.pdf
2025-06-09 19:35:37,509 - pdf_embedder - INFO - Generating embeddings for 5 texts using Ollama
2025-06-09 19:35:37,711 - pdf_embedder - INFO - Generated 5 embeddings total
2025-06-09 19:35:37,711 - pdf_embedder - INFO - Inserting 5 document chunks into database...
2025-06-09 19:35:40,010 - pdf_embedder - INFO - Database connection established successfully on attempt 1
2025-06-09 19:35:41,232 - pdf_embedder - INFO - Successfully inserted 5 document chunks
2025-06-09 19:35:41,233 - pdf_embedder - INFO - Successfully processed and stored 5 chunks for DYSENTERY.pdf
2025-06-09 19:35:41,233 - pdf_embedder - INFO - Processing file 2/34: MILK FEVER.pdf
2025-06-09 19:35:41,233 - pdf_embedder - INFO - Processing single PDF: MILK FEVER.pdf
2025-06-09 19:35:41,233 - pdf_embedder - INFO - Extracting text from PDF: diseases/MILK FEVER.pdf
2025-06-09 19:35:41,240 - pdf_embedder - INFO - Successfully extracted 688 characters from diseases/MILK FEVER.pdf
2025-06-09 19:35:41,240 - pdf_embedder - INFO - Created 6 chunks from MILK FEVER.pdf
2025-06-09 19:35:41,240 - pdf_embedder - INFO - Generating embeddings for 6 texts using Ollama
2025-06-09 19:35:41,419 - pdf_embedder - INFO - Generated 6 embeddings total
2025-06-09 19:35:41,419 - pdf_embedder - INFO - Inserting 6 document chunks into database...
2025-06-09 19:35:43,676 - pdf_embedder - INFO - Database connection established successfully on attempt 1
2025-06-09 19:35:45,101 - pdf_embedder - INFO - Successfully inserted 6 document chunks
2025-06-09 19:35:45,103 - pdf_embedder - INFO - Successfully processed and stored 6 chunks for MILK FEVER.pdf
2025-06-09 19:35:45,103 - pdf_embedder - INFO - Processing file 3/34: Dry Period Mastitis.pdf
2025-06-09 19:35:45,103 - pdf_embedder - INFO - Processing single PDF: Dry Period Mastitis.pdf
2025-06-09 19:35:45,103 - pdf_embedder - INFO - Extracting text from PDF: diseases/Dry Period Mastitis.pdf
2025-06-09 19:35:45,124 - pdf_embedder - INFO - Successfully extracted 317 characters from diseases/Dry Period Mastitis.pdf
2025-06-09 19:35:45,124 - pdf_embedder - INFO - Created 1 chunks from Dry Period Mastitis.pdf
2025-06-09 19:35:45,124 - pdf_embedder - INFO - Generating embeddings for 1 texts using Ollama
2025-06-09 19:35:45,197 - pdf_embedder - INFO - Generated 1 embeddings total
2025-06-09 19:35:45,197 - pdf_embedder - INFO - Inserting 1 document chunks into database...
2025-06-09 19:35:47,490 - pdf_embedder - INFO - Database connection established successfully on attempt 1
2025-06-09 19:35:48,358 - pdf_embedder - INFO - Successfully inserted 1 document chunks
2025-06-09 19:35:48,359 - pdf_embedder - INFO - Successfully processed and stored 1 chunks for Dry Period Mastitis.pdf
2025-06-09 19:35:48,360 - pdf_embedder - INFO - Processing file 4/34: PNEUMONIA.pdf
2025-06-09 19:35:48,360 - pdf_embedder - INFO - Processing single PDF: PNEUMONIA.pdf
2025-06-09 19:35:48,360 - pdf_embedder - INFO - Extracting text from PDF: diseases/PNEUMONIA.pdf
2025-06-09 19:35:48,373 - pdf_embedder - INFO - Successfully extracted 955 characters from diseases/PNEUMONIA.pdf
2025-06-09 19:35:48,374 - pdf_embedder - INFO - Created 6 chunks from PNEUMONIA.pdf
2025-06-09 19:35:48,374 - pdf_embedder - INFO - Generating embeddings for 6 texts using Ollama
2025-06-09 19:35:48,593 - pdf_embedder - INFO - Generated 6 embeddings total
2025-06-09 19:35:48,593 - pdf_embedder - INFO - Inserting 6 document chunks into database...
2025-06-09 19:35:50,892 - pdf_embedder - INFO - Database connection established successfully on attempt 1
2025-06-09 19:35:52,384 - pdf_embedder - INFO - Successfully inserted 6 document chunks
2025-06-09 19:35:52,385 - pdf_embedder - INFO - Successfully processed and stored 6 chunks for PNEUMONIA.pdf
2025-06-09 19:35:52,385 - pdf_embedder - INFO - Processing file 5/34: THEILERIOSIS.pdf
2025-06-09 19:35:52,385 - pdf_embedder - INFO - Processing single PDF: THEILERIOSIS.pdf
2025-06-09 19:35:52,385 - pdf_embedder - INFO - Extracting text from PDF: diseases/THEILERIOSIS.pdf
2025-06-09 19:35:52,403 - pdf_embedder - INFO - Successfully extracted 2020 characters from diseases/THEILERIOSIS.pdf
2025-06-09 19:35:52,404 - pdf_embedder - INFO - Created 10 chunks from THEILERIOSIS.pdf
2025-06-09 19:35:52,404 - pdf_embedder - INFO - Generating embeddings for 10 texts using Ollama
2025-06-09 19:35:52,808 - pdf_embedder - INFO - Generated 10 embeddings total
2025-06-09 19:35:52,808 - pdf_embedder - INFO - Inserting 10 document chunks into database...
2025-06-09 19:35:55,090 - pdf_embedder - INFO - Database connection established successfully on attempt 1
2025-06-09 19:35:56,790 - pdf_embedder - INFO - Successfully inserted 10 document chunks
2025-06-09 19:35:56,792 - pdf_embedder - INFO - Successfully processed and stored 10 chunks for THEILERIOSIS.pdf
2025-06-09 19:35:56,792 - pdf_embedder - INFO - Processing file 6/34: JAUNDICE.pdf
2025-06-09 19:35:56,792 - pdf_embedder - INFO - Processing single PDF: JAUNDICE.pdf
2025-06-09 19:35:56,792 - pdf_embedder - INFO - Extracting text from PDF: diseases/JAUNDICE.pdf
2025-06-09 19:35:56,800 - pdf_embedder - INFO - Successfully extracted 662 characters from diseases/JAUNDICE.pdf
2025-06-09 19:35:56,800 - pdf_embedder - INFO - Created 5 chunks from JAUNDICE.pdf
2025-06-09 19:35:56,800 - pdf_embedder - INFO - Generating embeddings for 5 texts using Ollama
2025-06-09 19:35:57,002 - pdf_embedder - INFO - Generated 5 embeddings total
2025-06-09 19:35:57,002 - pdf_embedder - INFO - Inserting 5 document chunks into database...
2025-06-09 19:35:59,334 - pdf_embedder - INFO - Database connection established successfully on attempt 1
2025-06-09 19:36:00,534 - pdf_embedder - INFO - Successfully inserted 5 document chunks
2025-06-09 19:36:00,535 - pdf_embedder - INFO - Successfully processed and stored 5 chunks for JAUNDICE.pdf
2025-06-09 19:36:00,535 - pdf_embedder - INFO - Processing file 7/34: TRYPANOSOMIASIS.pdf
2025-06-09 19:36:00,535 - pdf_embedder - INFO - Processing single PDF: TRYPANOSOMIASIS.pdf
2025-06-09 19:36:00,535 - pdf_embedder - INFO - Extracting text from PDF: diseases/TRYPANOSOMIASIS.pdf
2025-06-09 19:36:00,545 - pdf_embedder - INFO - Successfully extracted 854 characters from diseases/TRYPANOSOMIASIS.pdf
2025-06-09 19:36:00,545 - pdf_embedder - INFO - Created 6 chunks from TRYPANOSOMIASIS.pdf
2025-06-09 19:36:00,545 - pdf_embedder - INFO - Generating embeddings for 6 texts using Ollama
2025-06-09 19:36:00,770 - pdf_embedder - INFO - Generated 6 embeddings total
2025-06-09 19:36:00,770 - pdf_embedder - INFO - Inserting 6 document chunks into database...
2025-06-09 19:36:03,077 - pdf_embedder - INFO - Database connection established successfully on attempt 1
2025-06-09 19:36:04,619 - pdf_embedder - INFO - Successfully inserted 6 document chunks
2025-06-09 19:36:04,619 - pdf_embedder - INFO - Successfully processed and stored 6 chunks for TRYPANOSOMIASIS.pdf
2025-06-09 19:36:04,620 - pdf_embedder - INFO - Processing file 8/34: LISTERIOSIS.pdf
2025-06-09 19:36:04,620 - pdf_embedder - INFO - Processing single PDF: LISTERIOSIS.pdf
2025-06-09 19:36:04,620 - pdf_embedder - INFO - Extracting text from PDF: diseases/LISTERIOSIS.pdf
2025-06-09 19:36:04,633 - pdf_embedder - INFO - Successfully extracted 1787 characters from diseases/LISTERIOSIS.pdf
2025-06-09 19:36:04,633 - pdf_embedder - INFO - Created 7 chunks from LISTERIOSIS.pdf
2025-06-09 19:36:04,633 - pdf_embedder - INFO - Generating embeddings for 7 texts using Ollama
2025-06-09 19:36:04,902 - pdf_embedder - INFO - Generated 7 embeddings total
2025-06-09 19:36:04,902 - pdf_embedder - INFO - Inserting 7 document chunks into database...
2025-06-09 19:36:07,300 - pdf_embedder - INFO - Database connection established successfully on attempt 1
2025-06-09 19:36:08,824 - pdf_embedder - INFO - Successfully inserted 7 document chunks
2025-06-09 19:36:08,825 - pdf_embedder - INFO - Successfully processed and stored 7 chunks for LISTERIOSIS.pdf
2025-06-09 19:36:08,825 - pdf_embedder - INFO - Processing file 9/34: BABESIA.pdf
2025-06-09 19:36:08,825 - pdf_embedder - INFO - Processing single PDF: BABESIA.pdf
2025-06-09 19:36:08,825 - pdf_embedder - INFO - Extracting text from PDF: diseases/BABESIA.pdf
2025-06-09 19:36:08,830 - pdf_embedder - INFO - Successfully extracted 1368 characters from diseases/BABESIA.pdf
2025-06-09 19:36:08,830 - pdf_embedder - INFO - Created 7 chunks from BABESIA.pdf
2025-06-09 19:36:08,830 - pdf_embedder - INFO - Generating embeddings for 7 texts using Ollama
2025-06-09 19:36:09,128 - pdf_embedder - INFO - Generated 7 embeddings total
2025-06-09 19:36:09,129 - pdf_embedder - INFO - Inserting 7 document chunks into database...
2025-06-09 19:36:11,345 - pdf_embedder - INFO - Database connection established successfully on attempt 1
2025-06-09 19:36:12,787 - pdf_embedder - INFO - Successfully inserted 7 document chunks
2025-06-09 19:36:12,787 - pdf_embedder - INFO - Successfully processed and stored 7 chunks for BABESIA.pdf
2025-06-09 19:36:12,787 - pdf_embedder - INFO - Processing file 10/34: UTERINE TORSION.pdf
2025-06-09 19:36:12,787 - pdf_embedder - INFO - Processing single PDF: UTERINE TORSION.pdf
2025-06-09 19:36:12,787 - pdf_embedder - INFO - Extracting text from PDF: diseases/UTERINE TORSION.pdf
2025-06-09 19:36:12,801 - pdf_embedder - INFO - Successfully extracted 2220 characters from diseases/UTERINE TORSION.pdf
2025-06-09 19:36:12,801 - pdf_embedder - INFO - Created 10 chunks from UTERINE TORSION.pdf
2025-06-09 19:36:12,801 - pdf_embedder - INFO - Generating embeddings for 10 texts using Ollama
2025-06-09 19:36:13,169 - pdf_embedder - INFO - Generated 10 embeddings total
2025-06-09 19:36:13,169 - pdf_embedder - INFO - Inserting 10 document chunks into database...
2025-06-09 19:36:15,495 - pdf_embedder - INFO - Database connection established successfully on attempt 1
2025-06-09 19:36:17,258 - pdf_embedder - INFO - Successfully inserted 10 document chunks
2025-06-09 19:36:17,259 - pdf_embedder - INFO - Successfully processed and stored 10 chunks for UTERINE TORSION.pdf
2025-06-09 19:36:17,259 - pdf_embedder - INFO - Processing file 11/34: Chronic_Fibrosis Mastitis.pdf
2025-06-09 19:36:17,259 - pdf_embedder - INFO - Processing single PDF: Chronic_Fibrosis Mastitis.pdf
2025-06-09 19:36:17,259 - pdf_embedder - INFO - Extracting text from PDF: diseases/Chronic_Fibrosis Mastitis.pdf
2025-06-09 19:36:17,263 - pdf_embedder - INFO - Successfully extracted 444 characters from diseases/Chronic_Fibrosis Mastitis.pdf
2025-06-09 19:36:17,263 - pdf_embedder - INFO - Created 1 chunks from Chronic_Fibrosis Mastitis.pdf
2025-06-09 19:36:17,263 - pdf_embedder - INFO - Generating embeddings for 1 texts using Ollama
2025-06-09 19:36:17,362 - pdf_embedder - INFO - Generated 1 embeddings total
2025-06-09 19:36:17,363 - pdf_embedder - INFO - Inserting 1 document chunks into database...
2025-06-09 19:36:19,691 - pdf_embedder - INFO - Database connection established successfully on attempt 1
2025-06-09 19:36:20,557 - pdf_embedder - INFO - Successfully inserted 1 document chunks
2025-06-09 19:36:20,557 - pdf_embedder - INFO - Successfully processed and stored 1 chunks for Chronic_Fibrosis Mastitis.pdf
2025-06-09 19:36:20,557 - pdf_embedder - INFO - Processing file 12/34: _NAVAL ILL.pdf
2025-06-09 19:36:20,557 - pdf_embedder - INFO - Processing single PDF: _NAVAL ILL.pdf
2025-06-09 19:36:20,557 - pdf_embedder - INFO - Extracting text from PDF: diseases/_NAVAL ILL.pdf
2025-06-09 19:36:20,565 - pdf_embedder - INFO - Successfully extracted 1101 characters from diseases/_NAVAL ILL.pdf
2025-06-09 19:36:20,565 - pdf_embedder - INFO - Created 5 chunks from _NAVAL ILL.pdf
2025-06-09 19:36:20,565 - pdf_embedder - INFO - Generating embeddings for 5 texts using Ollama
2025-06-09 19:36:20,781 - pdf_embedder - INFO - Generated 5 embeddings total
2025-06-09 19:36:20,781 - pdf_embedder - INFO - Inserting 5 document chunks into database...
2025-06-09 19:36:23,108 - pdf_embedder - INFO - Database connection established successfully on attempt 1
2025-06-09 19:36:24,274 - pdf_embedder - INFO - Successfully inserted 5 document chunks
2025-06-09 19:36:24,275 - pdf_embedder - INFO - Successfully processed and stored 5 chunks for _NAVAL ILL.pdf
2025-06-09 19:36:24,275 - pdf_embedder - INFO - Processing file 13/34: Subclinical Mastitis.pdf
2025-06-09 19:36:24,275 - pdf_embedder - INFO - Processing single PDF: Subclinical Mastitis.pdf
2025-06-09 19:36:24,275 - pdf_embedder - INFO - Extracting text from PDF: diseases/Subclinical Mastitis.pdf
2025-06-09 19:36:24,278 - pdf_embedder - INFO - Successfully extracted 675 characters from diseases/Subclinical Mastitis.pdf
2025-06-09 19:36:24,278 - pdf_embedder - INFO - Created 1 chunks from Subclinical Mastitis.pdf
2025-06-09 19:36:24,278 - pdf_embedder - INFO - Generating embeddings for 1 texts using Ollama
2025-06-09 19:36:24,379 - pdf_embedder - INFO - Generated 1 embeddings total
2025-06-09 19:36:24,379 - pdf_embedder - INFO - Inserting 1 document chunks into database...
2025-06-09 19:36:26,762 - pdf_embedder - INFO - Database connection established successfully on attempt 1
2025-06-09 19:36:27,662 - pdf_embedder - INFO - Successfully inserted 1 document chunks
2025-06-09 19:36:27,662 - pdf_embedder - INFO - Successfully processed and stored 1 chunks for Subclinical Mastitis.pdf
2025-06-09 19:36:27,663 - pdf_embedder - INFO - Processing file 14/34: ACIDOSIS.pdf
2025-06-09 19:36:27,664 - pdf_embedder - INFO - Processing single PDF: ACIDOSIS.pdf
2025-06-09 19:36:27,665 - pdf_embedder - INFO - Extracting text from PDF: diseases/ACIDOSIS.pdf
2025-06-09 19:36:27,680 - pdf_embedder - INFO - Successfully extracted 1218 characters from diseases/ACIDOSIS.pdf
2025-06-09 19:36:27,680 - pdf_embedder - INFO - Created 4 chunks from ACIDOSIS.pdf
2025-06-09 19:36:27,680 - pdf_embedder - INFO - Generating embeddings for 4 texts using Ollama
2025-06-09 19:36:27,865 - pdf_embedder - INFO - Generated 4 embeddings total
2025-06-09 19:36:27,865 - pdf_embedder - INFO - Inserting 4 document chunks into database...
2025-06-09 19:36:30,148 - pdf_embedder - INFO - Database connection established successfully on attempt 1
2025-06-09 19:36:31,307 - pdf_embedder - INFO - Successfully inserted 4 document chunks
2025-06-09 19:36:31,308 - pdf_embedder - INFO - Successfully processed and stored 4 chunks for ACIDOSIS.pdf
2025-06-09 19:36:31,308 - pdf_embedder - INFO - Processing file 15/34: Per Acute Mastitis.pdf
2025-06-09 19:36:31,308 - pdf_embedder - INFO - Processing single PDF: Per Acute Mastitis.pdf
2025-06-09 19:36:31,308 - pdf_embedder - INFO - Extracting text from PDF: diseases/Per Acute Mastitis.pdf
2025-06-09 19:36:31,315 - pdf_embedder - INFO - Successfully extracted 540 characters from diseases/Per Acute Mastitis.pdf
2025-06-09 19:36:31,316 - pdf_embedder - INFO - Created 1 chunks from Per Acute Mastitis.pdf
2025-06-09 19:36:31,316 - pdf_embedder - INFO - Generating embeddings for 1 texts using Ollama
2025-06-09 19:36:31,409 - pdf_embedder - INFO - Generated 1 embeddings total
2025-06-09 19:36:31,410 - pdf_embedder - INFO - Inserting 1 document chunks into database...
2025-06-09 19:36:33,805 - pdf_embedder - INFO - Database connection established successfully on attempt 1
2025-06-09 19:36:34,675 - pdf_embedder - INFO - Successfully inserted 1 document chunks
2025-06-09 19:36:34,675 - pdf_embedder - INFO - Successfully processed and stored 1 chunks for Per Acute Mastitis.pdf
2025-06-09 19:36:34,675 - pdf_embedder - INFO - Processing file 16/34: DIARRHEA_ENTERITIS.pdf
2025-06-09 19:36:34,675 - pdf_embedder - INFO - Processing single PDF: DIARRHEA_ENTERITIS.pdf
2025-06-09 19:36:34,675 - pdf_embedder - INFO - Extracting text from PDF: diseases/DIARRHEA_ENTERITIS.pdf
2025-06-09 19:36:34,683 - pdf_embedder - INFO - Successfully extracted 1741 characters from diseases/DIARRHEA_ENTERITIS.pdf
2025-06-09 19:36:34,683 - pdf_embedder - INFO - Created 10 chunks from DIARRHEA_ENTERITIS.pdf
2025-06-09 19:36:34,683 - pdf_embedder - INFO - Generating embeddings for 10 texts using Ollama
2025-06-09 19:36:35,028 - pdf_embedder - INFO - Generated 10 embeddings total
2025-06-09 19:36:35,029 - pdf_embedder - INFO - Inserting 10 document chunks into database...
2025-06-09 19:36:37,339 - pdf_embedder - INFO - Database connection established successfully on attempt 1
2025-06-09 19:36:39,095 - pdf_embedder - INFO - Successfully inserted 10 document chunks
2025-06-09 19:36:39,096 - pdf_embedder - INFO - Successfully processed and stored 10 chunks for DIARRHEA_ENTERITIS.pdf
2025-06-09 19:36:39,096 - pdf_embedder - INFO - Processing file 17/34: Summer Mastitis.pdf
2025-06-09 19:36:39,097 - pdf_embedder - INFO - Processing single PDF: Summer Mastitis.pdf
2025-06-09 19:36:39,097 - pdf_embedder - INFO - Extracting text from PDF: diseases/Summer Mastitis.pdf
2025-06-09 19:36:39,105 - pdf_embedder - INFO - Successfully extracted 476 characters from diseases/Summer Mastitis.pdf
2025-06-09 19:36:39,105 - pdf_embedder - INFO - Created 1 chunks from Summer Mastitis.pdf
2025-06-09 19:36:39,105 - pdf_embedder - INFO - Generating embeddings for 1 texts using Ollama
2025-06-09 19:36:39,192 - pdf_embedder - INFO - Generated 1 embeddings total
2025-06-09 19:36:39,192 - pdf_embedder - INFO - Inserting 1 document chunks into database...
2025-06-09 19:36:41,537 - pdf_embedder - INFO - Database connection established successfully on attempt 1
2025-06-09 19:36:42,428 - pdf_embedder - INFO - Successfully inserted 1 document chunks
2025-06-09 19:36:42,429 - pdf_embedder - INFO - Successfully processed and stored 1 chunks for Summer Mastitis.pdf
2025-06-09 19:36:42,430 - pdf_embedder - INFO - Processing file 18/34: ANEMIA.pdf
2025-06-09 19:36:42,430 - pdf_embedder - INFO - Processing single PDF: ANEMIA.pdf
2025-06-09 19:36:42,430 - pdf_embedder - INFO - Extracting text from PDF: diseases/ANEMIA.pdf
2025-06-09 19:36:42,442 - pdf_embedder - INFO - Successfully extracted 1124 characters from diseases/ANEMIA.pdf
2025-06-09 19:36:42,443 - pdf_embedder - INFO - Created 7 chunks from ANEMIA.pdf
2025-06-09 19:36:42,443 - pdf_embedder - INFO - Generating embeddings for 7 texts using Ollama
2025-06-09 19:36:42,700 - pdf_embedder - INFO - Generated 7 embeddings total
2025-06-09 19:36:42,700 - pdf_embedder - INFO - Inserting 7 document chunks into database...
2025-06-09 19:36:45,124 - pdf_embedder - INFO - Database connection established successfully on attempt 1
2025-06-09 19:36:46,630 - pdf_embedder - INFO - Successfully inserted 7 document chunks
2025-06-09 19:36:46,631 - pdf_embedder - INFO - Successfully processed and stored 7 chunks for ANEMIA.pdf
2025-06-09 19:36:46,631 - pdf_embedder - INFO - Processing file 19/34: DYSTOCIA COMPLICATION.pdf
2025-06-09 19:36:46,631 - pdf_embedder - INFO - Processing single PDF: DYSTOCIA COMPLICATION.pdf
2025-06-09 19:36:46,632 - pdf_embedder - INFO - Extracting text from PDF: diseases/DYSTOCIA COMPLICATION.pdf
2025-06-09 19:36:46,644 - pdf_embedder - INFO - Successfully extracted 936 characters from diseases/DYSTOCIA COMPLICATION.pdf
2025-06-09 19:36:46,644 - pdf_embedder - INFO - Created 4 chunks from DYSTOCIA COMPLICATION.pdf
2025-06-09 19:36:46,644 - pdf_embedder - INFO - Generating embeddings for 4 texts using Ollama
2025-06-09 19:36:46,831 - pdf_embedder - INFO - Generated 4 embeddings total
2025-06-09 19:36:46,832 - pdf_embedder - INFO - Inserting 4 document chunks into database...
2025-06-09 19:36:49,242 - pdf_embedder - INFO - Database connection established successfully on attempt 1
2025-06-09 19:36:50,460 - pdf_embedder - INFO - Successfully inserted 4 document chunks
2025-06-09 19:36:50,461 - pdf_embedder - INFO - Successfully processed and stored 4 chunks for DYSTOCIA COMPLICATION.pdf
2025-06-09 19:36:50,461 - pdf_embedder - INFO - Processing file 20/34: Gangrenous Mastitis.pdf
2025-06-09 19:36:50,461 - pdf_embedder - INFO - Processing single PDF: Gangrenous Mastitis.pdf
2025-06-09 19:36:50,461 - pdf_embedder - INFO - Extracting text from PDF: diseases/Gangrenous Mastitis.pdf
2025-06-09 19:36:50,469 - pdf_embedder - INFO - Successfully extracted 531 characters from diseases/Gangrenous Mastitis.pdf
2025-06-09 19:36:50,470 - pdf_embedder - INFO - Created 1 chunks from Gangrenous Mastitis.pdf
2025-06-09 19:36:50,470 - pdf_embedder - INFO - Generating embeddings for 1 texts using Ollama
2025-06-09 19:36:50,546 - pdf_embedder - INFO - Generated 1 embeddings total
2025-06-09 19:36:50,546 - pdf_embedder - INFO - Inserting 1 document chunks into database...
2025-06-09 19:36:52,895 - pdf_embedder - INFO - Database connection established successfully on attempt 1
2025-06-09 19:36:53,782 - pdf_embedder - INFO - Successfully inserted 1 document chunks
2025-06-09 19:36:53,784 - pdf_embedder - INFO - Successfully processed and stored 1 chunks for Gangrenous Mastitis.pdf
2025-06-09 19:36:53,784 - pdf_embedder - INFO - Processing file 21/34: Mild Mastitis.pdf
2025-06-09 19:36:53,784 - pdf_embedder - INFO - Processing single PDF: Mild Mastitis.pdf
2025-06-09 19:36:53,784 - pdf_embedder - INFO - Extracting text from PDF: diseases/Mild Mastitis.pdf
2025-06-09 19:36:53,793 - pdf_embedder - INFO - Successfully extracted 500 characters from diseases/Mild Mastitis.pdf
2025-06-09 19:36:53,794 - pdf_embedder - INFO - Created 1 chunks from Mild Mastitis.pdf
2025-06-09 19:36:53,794 - pdf_embedder - INFO - Generating embeddings for 1 texts using Ollama
2025-06-09 19:36:53,869 - pdf_embedder - INFO - Generated 1 embeddings total
2025-06-09 19:36:53,869 - pdf_embedder - INFO - Inserting 1 document chunks into database...
2025-06-09 19:36:56,107 - pdf_embedder - INFO - Database connection established successfully on attempt 1
2025-06-09 19:36:56,938 - pdf_embedder - INFO - Successfully inserted 1 document chunks
2025-06-09 19:36:56,940 - pdf_embedder - INFO - Successfully processed and stored 1 chunks for Mild Mastitis.pdf
2025-06-09 19:36:56,940 - pdf_embedder - INFO - Processing file 22/34: EYE INFECTION.pdf
2025-06-09 19:36:56,940 - pdf_embedder - INFO - Processing single PDF: EYE INFECTION.pdf
2025-06-09 19:36:56,940 - pdf_embedder - INFO - Extracting text from PDF: diseases/EYE INFECTION.pdf
2025-06-09 19:36:56,961 - pdf_embedder - INFO - Successfully extracted 2131 characters from diseases/EYE INFECTION.pdf
2025-06-09 19:36:56,962 - pdf_embedder - INFO - Created 21 chunks from EYE INFECTION.pdf
2025-06-09 19:36:56,962 - pdf_embedder - INFO - Generating embeddings for 21 texts using Ollama
2025-06-09 19:36:57,677 - pdf_embedder - INFO - Generated 21 embeddings total
2025-06-09 19:36:57,677 - pdf_embedder - INFO - Inserting 21 document chunks into database...
2025-06-09 19:36:59,944 - pdf_embedder - INFO - Database connection established successfully on attempt 1
2025-06-09 19:37:02,232 - pdf_embedder - INFO - Successfully inserted 21 document chunks
2025-06-09 19:37:02,233 - pdf_embedder - INFO - Successfully processed and stored 21 chunks for EYE INFECTION.pdf
2025-06-09 19:37:02,233 - pdf_embedder - INFO - Processing file 23/34: TYMPANY_BLOAT.pdf
2025-06-09 19:37:02,233 - pdf_embedder - INFO - Processing single PDF: TYMPANY_BLOAT.pdf
2025-06-09 19:37:02,233 - pdf_embedder - INFO - Extracting text from PDF: diseases/TYMPANY_BLOAT.pdf
2025-06-09 19:37:02,249 - pdf_embedder - INFO - Successfully extracted 1275 characters from diseases/TYMPANY_BLOAT.pdf
2025-06-09 19:37:02,249 - pdf_embedder - INFO - Created 10 chunks from TYMPANY_BLOAT.pdf
2025-06-09 19:37:02,249 - pdf_embedder - INFO - Generating embeddings for 10 texts using Ollama
2025-06-09 19:37:02,664 - pdf_embedder - INFO - Generated 10 embeddings total
2025-06-09 19:37:02,664 - pdf_embedder - INFO - Inserting 10 document chunks into database...
2025-06-09 19:37:04,912 - pdf_embedder - INFO - Database connection established successfully on attempt 1
2025-06-09 19:37:06,626 - pdf_embedder - INFO - Successfully inserted 10 document chunks
2025-06-09 19:37:06,627 - pdf_embedder - INFO - Successfully processed and stored 10 chunks for TYMPANY_BLOAT.pdf
2025-06-09 19:37:06,627 - pdf_embedder - INFO - Processing file 24/34: SUB CLINICAL KETOSIS, KETOSIS & NERVINE KETOSIS.pdf
2025-06-09 19:37:06,627 - pdf_embedder - INFO - Processing single PDF: SUB CLINICAL KETOSIS, KETOSIS & NERVINE KETOSIS.pdf
2025-06-09 19:37:06,627 - pdf_embedder - INFO - Extracting text from PDF: diseases/SUB CLINICAL KETOSIS, KETOSIS & NERVINE KETOSIS.pdf
2025-06-09 19:37:06,635 - pdf_embedder - INFO - Successfully extracted 931 characters from diseases/SUB CLINICAL KETOSIS, KETOSIS & NERVINE KETOSIS.pdf
2025-06-09 19:37:06,635 - pdf_embedder - INFO - Created 5 chunks from SUB CLINICAL KETOSIS, KETOSIS & NERVINE KETOSIS.pdf
2025-06-09 19:37:06,635 - pdf_embedder - INFO - Generating embeddings for 5 texts using Ollama
2025-06-09 19:37:06,832 - pdf_embedder - INFO - Generated 5 embeddings total
2025-06-09 19:37:06,832 - pdf_embedder - INFO - Inserting 5 document chunks into database...
2025-06-09 19:37:09,110 - pdf_embedder - INFO - Database connection established successfully on attempt 1
2025-06-09 19:37:10,277 - pdf_embedder - INFO - Successfully inserted 5 document chunks
2025-06-09 19:37:10,278 - pdf_embedder - INFO - Successfully processed and stored 5 chunks for SUB CLINICAL KETOSIS, KETOSIS & NERVINE KETOSIS.pdf
2025-06-09 19:37:10,278 - pdf_embedder - INFO - Processing file 25/34: RETENTION OF PLACENTA.pdf
2025-06-09 19:37:10,278 - pdf_embedder - INFO - Processing single PDF: RETENTION OF PLACENTA.pdf
2025-06-09 19:37:10,278 - pdf_embedder - INFO - Extracting text from PDF: diseases/RETENTION OF PLACENTA.pdf
2025-06-09 19:37:10,289 - pdf_embedder - INFO - Successfully extracted 1497 characters from diseases/RETENTION OF PLACENTA.pdf
2025-06-09 19:37:10,289 - pdf_embedder - INFO - Created 8 chunks from RETENTION OF PLACENTA.pdf
2025-06-09 19:37:10,289 - pdf_embedder - INFO - Generating embeddings for 8 texts using Ollama
2025-06-09 19:37:10,583 - pdf_embedder - INFO - Generated 8 embeddings total
2025-06-09 19:37:10,583 - pdf_embedder - INFO - Inserting 8 document chunks into database...
2025-06-09 19:37:12,929 - pdf_embedder - INFO - Database connection established successfully on attempt 1
2025-06-09 19:37:14,444 - pdf_embedder - INFO - Successfully inserted 8 document chunks
2025-06-09 19:37:14,445 - pdf_embedder - INFO - Successfully processed and stored 8 chunks for RETENTION OF PLACENTA.pdf
2025-06-09 19:37:14,446 - pdf_embedder - INFO - Processing file 26/34: STRESS.pdf
2025-06-09 19:37:14,446 - pdf_embedder - INFO - Processing single PDF: STRESS.pdf
2025-06-09 19:37:14,446 - pdf_embedder - INFO - Extracting text from PDF: diseases/STRESS.pdf
2025-06-09 19:37:14,465 - pdf_embedder - INFO - Successfully extracted 2342 characters from diseases/STRESS.pdf
2025-06-09 19:37:14,466 - pdf_embedder - INFO - Created 16 chunks from STRESS.pdf
2025-06-09 19:37:14,466 - pdf_embedder - INFO - Generating embeddings for 16 texts using Ollama
2025-06-09 19:37:14,970 - pdf_embedder - INFO - Generated 16 embeddings total
2025-06-09 19:37:14,971 - pdf_embedder - INFO - Inserting 16 document chunks into database...
2025-06-09 19:37:17,263 - pdf_embedder - INFO - Database connection established successfully on attempt 1
2025-06-09 19:37:19,314 - pdf_embedder - INFO - Successfully inserted 16 document chunks
2025-06-09 19:37:19,315 - pdf_embedder - INFO - Successfully processed and stored 16 chunks for STRESS.pdf
2025-06-09 19:37:19,315 - pdf_embedder - INFO - Processing file 27/34: EPHEMERAL FEVER.pdf
2025-06-09 19:37:19,316 - pdf_embedder - INFO - Processing single PDF: EPHEMERAL FEVER.pdf
2025-06-09 19:37:19,316 - pdf_embedder - INFO - Extracting text from PDF: diseases/EPHEMERAL FEVER.pdf
2025-06-09 19:37:19,329 - pdf_embedder - INFO - Successfully extracted 891 characters from diseases/EPHEMERAL FEVER.pdf
2025-06-09 19:37:19,329 - pdf_embedder - INFO - Created 6 chunks from EPHEMERAL FEVER.pdf
2025-06-09 19:37:19,329 - pdf_embedder - INFO - Generating embeddings for 6 texts using Ollama
2025-06-09 19:37:19,553 - pdf_embedder - INFO - Generated 6 embeddings total
2025-06-09 19:37:19,554 - pdf_embedder - INFO - Inserting 6 document chunks into database...
2025-06-09 19:37:21,858 - pdf_embedder - INFO - Database connection established successfully on attempt 1
2025-06-09 19:37:23,312 - pdf_embedder - INFO - Successfully inserted 6 document chunks
2025-06-09 19:37:23,314 - pdf_embedder - INFO - Successfully processed and stored 6 chunks for EPHEMERAL FEVER.pdf
2025-06-09 19:37:23,314 - pdf_embedder - INFO - Processing file 28/34: Acute Mastitis.pdf
2025-06-09 19:37:23,314 - pdf_embedder - INFO - Processing single PDF: Acute Mastitis.pdf
2025-06-09 19:37:23,314 - pdf_embedder - INFO - Extracting text from PDF: diseases/Acute Mastitis.pdf
2025-06-09 19:37:23,325 - pdf_embedder - INFO - Successfully extracted 525 characters from diseases/Acute Mastitis.pdf
2025-06-09 19:37:23,325 - pdf_embedder - INFO - Created 1 chunks from Acute Mastitis.pdf
2025-06-09 19:37:23,325 - pdf_embedder - INFO - Generating embeddings for 1 texts using Ollama
2025-06-09 19:37:23,409 - pdf_embedder - INFO - Generated 1 embeddings total
2025-06-09 19:37:23,409 - pdf_embedder - INFO - Inserting 1 document chunks into database...
2025-06-09 19:37:25,673 - pdf_embedder - INFO - Database connection established successfully on attempt 1
2025-06-09 19:37:26,530 - pdf_embedder - INFO - Successfully inserted 1 document chunks
2025-06-09 19:37:26,531 - pdf_embedder - INFO - Successfully processed and stored 1 chunks for Acute Mastitis.pdf
2025-06-09 19:37:26,532 - pdf_embedder - INFO - Processing file 29/34: ERGOT POISONING (DEGNALA DISEASE).pdf
2025-06-09 19:37:26,532 - pdf_embedder - INFO - Processing single PDF: ERGOT POISONING (DEGNALA DISEASE).pdf
2025-06-09 19:37:26,533 - pdf_embedder - INFO - Extracting text from PDF: diseases/ERGOT POISONING (DEGNALA DISEASE).pdf
2025-06-09 19:37:26,551 - pdf_embedder - INFO - Successfully extracted 1706 characters from diseases/ERGOT POISONING (DEGNALA DISEASE).pdf
2025-06-09 19:37:26,552 - pdf_embedder - INFO - Created 5 chunks from ERGOT POISONING (DEGNALA DISEASE).pdf
2025-06-09 19:37:26,552 - pdf_embedder - INFO - Generating embeddings for 5 texts using Ollama
2025-06-09 19:37:26,820 - pdf_embedder - INFO - Generated 5 embeddings total
2025-06-09 19:37:26,820 - pdf_embedder - INFO - Inserting 5 document chunks into database...
2025-06-09 19:37:29,132 - pdf_embedder - INFO - Database connection established successfully on attempt 1
2025-06-09 19:37:30,358 - pdf_embedder - INFO - Successfully inserted 5 document chunks
2025-06-09 19:37:30,359 - pdf_embedder - INFO - Successfully processed and stored 5 chunks for ERGOT POISONING (DEGNALA DISEASE).pdf
2025-06-09 19:37:30,360 - pdf_embedder - INFO - Processing file 30/34: ANAPLASMOSIS.pdf
2025-06-09 19:37:30,360 - pdf_embedder - INFO - Processing single PDF: ANAPLASMOSIS.pdf
2025-06-09 19:37:30,360 - pdf_embedder - INFO - Extracting text from PDF: diseases/ANAPLASMOSIS.pdf
2025-06-09 19:37:30,369 - pdf_embedder - INFO - Successfully extracted 511 characters from diseases/ANAPLASMOSIS.pdf
2025-06-09 19:37:30,370 - pdf_embedder - INFO - Created 4 chunks from ANAPLASMOSIS.pdf
2025-06-09 19:37:30,370 - pdf_embedder - INFO - Generating embeddings for 4 texts using Ollama
2025-06-09 19:37:30,532 - pdf_embedder - INFO - Generated 4 embeddings total
2025-06-09 19:37:30,533 - pdf_embedder - INFO - Inserting 4 document chunks into database...
2025-06-09 19:37:32,852 - pdf_embedder - INFO - Database connection established successfully on attempt 1
2025-06-09 19:37:34,053 - pdf_embedder - INFO - Successfully inserted 4 document chunks
2025-06-09 19:37:34,054 - pdf_embedder - INFO - Successfully processed and stored 4 chunks for ANAPLASMOSIS.pdf
2025-06-09 19:37:34,054 - pdf_embedder - INFO - Processing file 31/34: Sub-Acute Mastitis.pdf
2025-06-09 19:37:34,055 - pdf_embedder - INFO - Processing single PDF: Sub-Acute Mastitis.pdf
2025-06-09 19:37:34,055 - pdf_embedder - INFO - Extracting text from PDF: diseases/Sub-Acute Mastitis.pdf
2025-06-09 19:37:34,064 - pdf_embedder - INFO - Successfully extracted 494 characters from diseases/Sub-Acute Mastitis.pdf
2025-06-09 19:37:34,065 - pdf_embedder - INFO - Created 1 chunks from Sub-Acute Mastitis.pdf
2025-06-09 19:37:34,065 - pdf_embedder - INFO - Generating embeddings for 1 texts using Ollama
2025-06-09 19:37:34,156 - pdf_embedder - INFO - Generated 1 embeddings total
2025-06-09 19:37:34,156 - pdf_embedder - INFO - Inserting 1 document chunks into database...
2025-06-09 19:37:36,557 - pdf_embedder - INFO - Database connection established successfully on attempt 1
2025-06-09 19:37:37,464 - pdf_embedder - INFO - Successfully inserted 1 document chunks
2025-06-09 19:37:37,465 - pdf_embedder - INFO - Successfully processed and stored 1 chunks for Sub-Acute Mastitis.pdf
2025-06-09 19:37:37,465 - pdf_embedder - INFO - Processing file 32/34: SKIN INFECTION.pdf
2025-06-09 19:37:37,466 - pdf_embedder - INFO - Processing single PDF: SKIN INFECTION.pdf
2025-06-09 19:37:37,466 - pdf_embedder - INFO - Extracting text from PDF: diseases/SKIN INFECTION.pdf
2025-06-09 19:37:37,476 - pdf_embedder - INFO - Successfully extracted 787 characters from diseases/SKIN INFECTION.pdf
2025-06-09 19:37:37,476 - pdf_embedder - INFO - Created 5 chunks from SKIN INFECTION.pdf
2025-06-09 19:37:37,476 - pdf_embedder - INFO - Generating embeddings for 5 texts using Ollama
2025-06-09 19:37:37,663 - pdf_embedder - INFO - Generated 5 embeddings total
2025-06-09 19:37:37,664 - pdf_embedder - INFO - Inserting 5 document chunks into database...
2025-06-09 19:37:39,933 - pdf_embedder - INFO - Database connection established successfully on attempt 1
2025-06-09 19:37:41,114 - pdf_embedder - INFO - Successfully inserted 5 document chunks
2025-06-09 19:37:41,115 - pdf_embedder - INFO - Successfully processed and stored 5 chunks for SKIN INFECTION.pdf
2025-06-09 19:37:41,115 - pdf_embedder - INFO - Processing file 33/34: COLIC PAIN IN ADULT.pdf
2025-06-09 19:37:41,116 - pdf_embedder - INFO - Processing single PDF: COLIC PAIN IN ADULT.pdf
2025-06-09 19:37:41,116 - pdf_embedder - INFO - Extracting text from PDF: diseases/COLIC PAIN IN ADULT.pdf
2025-06-09 19:37:41,132 - pdf_embedder - INFO - Successfully extracted 1656 characters from diseases/COLIC PAIN IN ADULT.pdf
2025-06-09 19:37:41,132 - pdf_embedder - INFO - Created 14 chunks from COLIC PAIN IN ADULT.pdf
2025-06-09 19:37:41,132 - pdf_embedder - INFO - Generating embeddings for 14 texts using Ollama
2025-06-09 19:37:41,592 - pdf_embedder - INFO - Generated 14 embeddings total
2025-06-09 19:37:41,592 - pdf_embedder - INFO - Inserting 14 document chunks into database...
2025-06-09 19:37:44,114 - pdf_embedder - INFO - Database connection established successfully on attempt 1
2025-06-09 19:37:46,166 - pdf_embedder - INFO - Successfully inserted 14 document chunks
2025-06-09 19:37:46,167 - pdf_embedder - INFO - Successfully processed and stored 14 chunks for COLIC PAIN IN ADULT.pdf
2025-06-09 19:37:46,167 - pdf_embedder - INFO - Processing file 34/34: IMPACTION OF RUMEN.pdf
2025-06-09 19:37:46,168 - pdf_embedder - INFO - Processing single PDF: IMPACTION OF RUMEN.pdf
2025-06-09 19:37:46,168 - pdf_embedder - INFO - Extracting text from PDF: diseases/IMPACTION OF RUMEN.pdf
2025-06-09 19:37:46,179 - pdf_embedder - INFO - Successfully extracted 838 characters from diseases/IMPACTION OF RUMEN.pdf
2025-06-09 19:37:46,180 - pdf_embedder - INFO - Created 6 chunks from IMPACTION OF RUMEN.pdf
2025-06-09 19:37:46,180 - pdf_embedder - INFO - Generating embeddings for 6 texts using Ollama
2025-06-09 19:37:46,397 - pdf_embedder - INFO - Generated 6 embeddings total
2025-06-09 19:37:46,397 - pdf_embedder - INFO - Inserting 6 document chunks into database...
2025-06-09 19:37:48,732 - pdf_embedder - INFO - Database connection established successfully on attempt 1
2025-06-09 19:37:50,244 - pdf_embedder - INFO - Successfully inserted 6 document chunks
2025-06-09 19:37:50,246 - pdf_embedder - INFO - Successfully processed and stored 6 chunks for IMPACTION OF RUMEN.pdf
2025-06-09 19:37:52,582 - pdf_embedder - INFO - Database connection established successfully on attempt 1
2025-06-09 19:37:53,167 - pdf_embedder - INFO - Processing complete! Total documents in database: 201
2025-06-09 19:38:35,847 - pdf_embedder - INFO - Initialized HuggingFaceEmbedder with model: sentence-transformers/all-MiniLM-L6-v2
2025-06-09 19:38:35,847 - pdf_embedder - INFO - Using API URL: https://api-inference.huggingface.co/models/sentence-transformers/all-MiniLM-L6-v2
2025-06-09 19:38:35,847 - pdf_embedder - INFO - Generating embeddings for 2 texts using Hugging Face
2025-06-09 19:38:36,919 - pdf_embedder - ERROR - HF API error for text 0: 400 - {"error":"SentenceSimilarityPipeline.__call__() missing 1 required positional argument: 'sentences'"}
2025-06-09 19:38:36,919 - pdf_embedder - ERROR - Request URL: https://api-inference.huggingface.co/models/sentence-transformers/all-MiniLM-L6-v2
2025-06-09 19:38:36,919 - pdf_embedder - ERROR - Model: sentence-transformers/all-MiniLM-L6-v2
2025-06-09 19:38:41,672 - pdf_embedder - ERROR - HF API error for text 1: 400 - {"error":"SentenceSimilarityPipeline.__call__() missing 1 required positional argument: 'sentences'"}
2025-06-09 19:38:41,672 - pdf_embedder - ERROR - Request URL: https://api-inference.huggingface.co/models/sentence-transformers/all-MiniLM-L6-v2
2025-06-09 19:38:41,673 - pdf_embedder - ERROR - Model: sentence-transformers/all-MiniLM-L6-v2
2025-06-09 19:38:41,673 - pdf_embedder - INFO - Generated 2 embeddings using Hugging Face
2025-06-09 19:39:05,611 - pdf_embedder - INFO - Initialized SentenceTransformersEmbedder with model: sentence-transformers/all-MiniLM-L6-v2
2025-06-09 19:39:07,954 - pdf_embedder - INFO - Loading Sentence Transformers model: sentence-transformers/all-MiniLM-L6-v2
2025-06-09 19:39:08,011 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: mps
2025-06-09 19:39:08,011 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
2025-06-09 19:39:25,703 - pdf_embedder - INFO - Sentence Transformers model loaded successfully
2025-06-09 19:39:25,705 - pdf_embedder - INFO - Generating embeddings for 2 texts using Sentence Transformers
2025-06-09 19:39:27,372 - pdf_embedder - INFO - Generated 2 embeddings using Sentence Transformers
2025-06-09 19:39:27,372 - pdf_embedder - INFO - Initialized HuggingFaceEmbedder with model: sentence-transformers/all-MiniLM-L6-v2
2025-06-09 19:39:27,372 - pdf_embedder - INFO - Using API URL: https://api-inference.huggingface.co/models/sentence-transformers/all-MiniLM-L6-v2
2025-06-09 19:39:27,372 - pdf_embedder - INFO - Generating embeddings for 2 texts using Hugging Face
2025-06-09 19:39:27,736 - pdf_embedder - ERROR - HF API error for text 0: 400 - {"error":"SentenceSimilarityPipeline.__call__() missing 1 required positional argument: 'sentences'"}
2025-06-09 19:39:27,736 - pdf_embedder - ERROR - Request URL: https://api-inference.huggingface.co/models/sentence-transformers/all-MiniLM-L6-v2
2025-06-09 19:39:27,736 - pdf_embedder - ERROR - Model: sentence-transformers/all-MiniLM-L6-v2
2025-06-09 19:39:28,119 - pdf_embedder - ERROR - HF API error for text 1: 400 - {"error":"SentenceSimilarityPipeline.__call__() missing 1 required positional argument: 'sentences'"}
2025-06-09 19:39:28,120 - pdf_embedder - ERROR - Request URL: https://api-inference.huggingface.co/models/sentence-transformers/all-MiniLM-L6-v2
2025-06-09 19:39:28,120 - pdf_embedder - ERROR - Model: sentence-transformers/all-MiniLM-L6-v2
2025-06-09 19:39:28,120 - pdf_embedder - INFO - Generated 2 embeddings using Hugging Face
2025-06-09 19:39:28,120 - pdf_embedder - INFO - Initialized NomicEmbedder with Ollama - base_url: http://localhost:11434, model: nomic-embed-text:latest
2025-06-09 19:39:28,121 - pdf_embedder - INFO - Generating embeddings for 2 texts using Ollama
2025-06-09 19:39:28,490 - pdf_embedder - INFO - Generated 2 embeddings total
2025-06-09 19:41:44,118 - search_system - INFO - Initializing MedicalSearchSystem with embedder type: nomic
2025-06-09 19:41:44,118 - pdf_embedder - INFO - Initialized NomicEmbedder with Ollama - base_url: http://localhost:11434, model: nomic-embed-text:latest
2025-06-09 19:41:44,118 - pdf_embedder - INFO - Using DATABASE_URL for database connection (production mode)
2025-06-09 19:41:44,118 - pdf_embedder - INFO - Using table name: documents
2025-06-09 19:41:44,118 - search_system - INFO - Initialized VectorSearchEngine with nomic embedder and PostgreSQLManager
2025-06-09 19:41:44,118 - search_system - INFO - Initialized GroqLLM with model: llama-3.3-70b-versatile
2025-06-09 19:41:44,118 - search_system - INFO - MedicalSearchSystem initialized successfully
2025-06-09 19:41:44,119 - search_system - INFO - Starting search and answer for query: 'test query...' (top_k=1)
2025-06-09 19:41:44,119 - search_system - INFO - Starting vector search for query: 'test query...' (top_k=1)
2025-06-09 19:41:44,119 - pdf_embedder - INFO - Generating embeddings for 1 texts using Ollama
2025-06-09 19:41:44,209 - pdf_embedder - INFO - Generated 1 embeddings total
2025-06-09 19:41:46,568 - pdf_embedder - INFO - Database connection established successfully on attempt 1
2025-06-09 19:41:47,413 - search_system - INFO - Found 1 raw results from database
2025-06-09 19:41:47,414 - search_system - INFO - Successfully processed 1 documents from search results
2025-06-09 19:41:47,683 - search_system - INFO - Found 1 relevant documents
2025-06-09 19:41:47,684 - search_system - INFO - Generating response with Groq LLM...
2025-06-09 19:41:47,684 - search_system - INFO - Generating LLM response for query: 'test query...'
2025-06-09 19:41:49,002 - search_system - INFO - Successfully generated LLM response (1480 characters)
2025-06-09 19:41:49,002 - search_system - INFO - Search and answer completed successfully - found 1 documents, generated 1480 character response
2025-06-09 19:42:54,118 - __main__ - INFO - 🚀 Starting Medical Document Search System API...
2025-06-09 19:42:54,118 - __main__ - INFO - 🔄 Initializing systems (attempt 1/3)...
2025-06-09 19:42:54,118 - __main__ - INFO - Initializing Flask app systems...
2025-06-09 19:42:54,118 - __main__ - INFO - Using embedder type: huggingface
2025-06-09 19:42:54,118 - search_system - INFO - Initializing MedicalSearchSystem with embedder type: huggingface
2025-06-09 19:42:54,118 - pdf_embedder - INFO - Initialized HuggingFaceEmbedder with model: sentence-transformers/all-MiniLM-L6-v2
2025-06-09 19:42:54,118 - pdf_embedder - INFO - Using API URL: https://api-inference.huggingface.co/models/sentence-transformers/all-MiniLM-L6-v2
2025-06-09 19:42:54,118 - pdf_embedder - INFO - Using DATABASE_URL for database connection (production mode)
2025-06-09 19:42:54,118 - pdf_embedder - INFO - Using table name: documents
2025-06-09 19:42:54,118 - search_system - INFO - Initialized VectorSearchEngine with huggingface embedder and PostgreSQLManager
2025-06-09 19:42:54,118 - search_system - INFO - Initialized GroqLLM with model: llama-3.3-70b-versatile
2025-06-09 19:42:54,118 - search_system - INFO - MedicalSearchSystem initialized successfully
2025-06-09 19:42:54,118 - pdf_embedder - INFO - Initializing PDFEmbeddingPipeline with embedder type: huggingface
2025-06-09 19:42:54,118 - pdf_embedder - INFO - Initialized HuggingFaceEmbedder with model: sentence-transformers/all-MiniLM-L6-v2
2025-06-09 19:42:54,118 - pdf_embedder - INFO - Using API URL: https://api-inference.huggingface.co/models/sentence-transformers/all-MiniLM-L6-v2
2025-06-09 19:42:54,118 - pdf_embedder - INFO - Initialized PDFProcessor with chunk_size: 1000, chunk_overlap: 200
2025-06-09 19:42:54,119 - pdf_embedder - INFO - Using DATABASE_URL for database connection (production mode)
2025-06-09 19:42:54,119 - pdf_embedder - INFO - Using table name: documents
2025-06-09 19:42:54,119 - pdf_embedder - INFO - PDFEmbeddingPipeline initialized successfully
2025-06-09 19:42:54,119 - __main__ - INFO - Flask app systems initialized successfully
2025-06-09 19:42:54,119 - __main__ - INFO - ✅ Systems initialized successfully
2025-06-09 19:42:54,119 - __main__ - INFO - 🌐 Starting Flask server on http://0.0.0.0:5001
2025-06-09 19:42:54,119 - __main__ - INFO - 📚 Web interface available at http://0.0.0.0:5001
2025-06-09 19:42:54,119 - __main__ - INFO - 🔍 API endpoints available at http://0.0.0.0:5001/api/
2025-06-09 19:42:54,119 - __main__ - INFO - 🌍 Environment: development
2025-06-09 19:42:54,119 - __main__ - INFO - 🐛 Debug mode: False
