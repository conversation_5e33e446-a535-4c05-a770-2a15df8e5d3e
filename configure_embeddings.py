#!/usr/bin/env python3
"""
Configuration script for setting up different embedding services
"""

import os
from pathlib import Path

def update_env_file(key: str, value: str):
    """Update or add a key-value pair in .env file"""
    env_file = Path(".env")
    
    # Read existing content
    lines = []
    if env_file.exists():
        with open(env_file, 'r') as f:
            lines = f.readlines()
    
    # Update or add the key
    key_found = False
    for i, line in enumerate(lines):
        if line.strip().startswith(f"{key}="):
            lines[i] = f"{key}={value}\n"
            key_found = True
            break
    
    if not key_found:
        lines.append(f"{key}={value}\n")
    
    # Write back to file
    with open(env_file, 'w') as f:
        f.writelines(lines)
    
    print(f"✅ Updated .env: {key}={value}")

def configure_nomic_api():
    """Configure Nomic AI API"""
    print("\n🎯 Configuring Nomic AI API")
    print("Get your API key from: https://atlas.nomic.ai/")
    
    api_key = input("Enter your Nomic API key: ").strip()
    if api_key:
        update_env_file("NOMIC_API_KEY", api_key)
        update_env_file("EMBEDDER_TYPE", "nomic_api")
        update_env_file("EMBEDDING_MODEL", "nomic-embed-text-v1.5")
        print("✅ Nomic AI configured successfully!")
    else:
        print("❌ No API key provided")

def configure_openai():
    """Configure OpenAI API"""
    print("\n🤖 Configuring OpenAI API")
    print("Get your API key from: https://platform.openai.com/api-keys")
    
    api_key = input("Enter your OpenAI API key: ").strip()
    if api_key:
        print("\nChoose model:")
        print("1. text-embedding-3-small (cheaper, good quality)")
        print("2. text-embedding-3-large (expensive, best quality)")
        
        choice = input("Enter choice (1 or 2): ").strip()
        model = "text-embedding-3-small" if choice == "1" else "text-embedding-3-large"
        
        update_env_file("OPENAI_API_KEY", api_key)
        update_env_file("EMBEDDER_TYPE", "openai")
        update_env_file("EMBEDDING_MODEL", model)
        print("✅ OpenAI configured successfully!")
    else:
        print("❌ No API key provided")

def configure_huggingface():
    """Configure Hugging Face API"""
    print("\n🤗 Configuring Hugging Face API")
    print("⚠️  Note: HF Inference API can be unreliable. Consider Sentence Transformers instead.")
    print("Get your token from: https://huggingface.co/settings/tokens")

    api_key = input("Enter your Hugging Face token (or press Enter to skip): ").strip()
    if api_key:
        print("\nChoose model:")
        print("1. sentence-transformers/all-MiniLM-L6-v2 (fast, good quality)")
        print("2. sentence-transformers/all-mpnet-base-v2 (best quality)")

        choice = input("Enter choice (1 or 2): ").strip()
        models = {
            "1": "sentence-transformers/all-MiniLM-L6-v2",
            "2": "sentence-transformers/all-mpnet-base-v2"
        }
        model = models.get(choice, models["1"])

        update_env_file("HUGGINGFACE_API_KEY", api_key)
        update_env_file("EMBEDDER_TYPE", "huggingface")
        update_env_file("EMBEDDING_MODEL", model)
        print("✅ Hugging Face configured successfully!")
        print("💡 If you get 404 errors, try Sentence Transformers instead (option 6)")
    else:
        print("❌ No API key provided")

def configure_sentence_transformers():
    """Configure Sentence Transformers (local)"""
    print("\n🏠 Configuring Sentence Transformers (Local)")
    print("This runs locally and doesn't require an API key!")

    try:
        import sentence_transformers
        print("✅ sentence-transformers library is already installed")
    except ImportError:
        print("📦 Installing sentence-transformers library...")
        import subprocess
        try:
            subprocess.check_call(["pip", "install", "sentence-transformers"])
            print("✅ sentence-transformers installed successfully!")
        except subprocess.CalledProcessError:
            print("❌ Failed to install sentence-transformers")
            print("Please run: pip install sentence-transformers")
            return

    print("\nChoose model:")
    print("1. all-MiniLM-L6-v2 (fast, 384 dimensions)")
    print("2. all-mpnet-base-v2 (better quality, 768 dimensions)")
    print("3. all-MiniLM-L12-v2 (balanced, 384 dimensions)")

    choice = input("Enter choice (1, 2, or 3): ").strip()
    models = {
        "1": "all-MiniLM-L6-v2",
        "2": "all-mpnet-base-v2",
        "3": "all-MiniLM-L12-v2"
    }
    model = models.get(choice, models["1"])

    update_env_file("EMBEDDER_TYPE", "sentence_transformers")
    update_env_file("EMBEDDING_MODEL", model)
    print("✅ Sentence Transformers configured successfully!")
    print("💡 This will download the model on first use (~100MB)")

def configure_together():
    """Configure Together AI API"""
    print("\n🚀 Configuring Together AI API")
    print("Get your API key from: https://api.together.xyz/settings/api-keys")
    
    api_key = input("Enter your Together AI API key: ").strip()
    if api_key:
        update_env_file("TOGETHER_API_KEY", api_key)
        update_env_file("EMBEDDER_TYPE", "together")
        update_env_file("EMBEDDING_MODEL", "togethercomputer/m2-bert-80M-8k-retrieval")
        print("✅ Together AI configured successfully!")
    else:
        print("❌ No API key provided")

def configure_ollama():
    """Configure local Ollama"""
    print("\n🏠 Configuring Local Ollama")
    print("Make sure Ollama is running with: ollama serve")
    print("And pull the model with: ollama pull nomic-embed-text")
    
    base_url = input("Enter Ollama base URL (default: http://localhost:11434): ").strip()
    if not base_url:
        base_url = "http://localhost:11434"
    
    update_env_file("EMBEDDER_TYPE", "ollama")
    update_env_file("OLLAMA_BASE_URL", base_url)
    print("✅ Ollama configured successfully!")

def show_current_config():
    """Show current configuration"""
    print("\n📋 Current Configuration:")
    env_file = Path(".env")
    
    if not env_file.exists():
        print("❌ No .env file found")
        return
    
    relevant_keys = [
        "EMBEDDER_TYPE", "NOMIC_API_KEY", "OPENAI_API_KEY", 
        "HUGGINGFACE_API_KEY", "TOGETHER_API_KEY", "EMBEDDING_MODEL",
        "OLLAMA_BASE_URL"
    ]
    
    with open(env_file, 'r') as f:
        for line in f:
            line = line.strip()
            if line and not line.startswith('#'):
                key, _, value = line.partition('=')
                if key in relevant_keys:
                    # Hide API keys for security
                    if "API_KEY" in key or "TOKEN" in key:
                        display_value = value[:8] + "..." if len(value) > 8 else "***"
                    else:
                        display_value = value
                    print(f"  {key}: {display_value}")

def main():
    """Main configuration menu"""
    print("🔗 Embedding Service Configuration")
    print("=" * 50)
    
    while True:
        print("\nChoose an embedding service:")
        print("1. 🎯 Nomic AI (Recommended - Cloud)")
        print("2. 🤖 OpenAI (Enterprise Grade)")
        print("3. 🤗 Hugging Face API (Can be unreliable)")
        print("4. 🚀 Together AI (Fast & Cheap)")
        print("5. 🏠 Local Ollama")
        print("6. 🏠 Sentence Transformers (Local, No API)")
        print("7. 📋 Show current configuration")
        print("8. ❌ Exit")

        choice = input("\nEnter your choice (1-8): ").strip()

        if choice == "1":
            configure_nomic_api()
        elif choice == "2":
            configure_openai()
        elif choice == "3":
            configure_huggingface()
        elif choice == "4":
            configure_together()
        elif choice == "5":
            configure_ollama()
        elif choice == "6":
            configure_sentence_transformers()
        elif choice == "7":
            show_current_config()
        elif choice == "8":
            print("\n👋 Configuration complete!")
            break
        else:
            print("❌ Invalid choice. Please try again.")

        if choice in ["1", "2", "3", "4", "5", "6"]:
            print("\n🔄 Restart your application to use the new configuration.")
            restart = input("Continue configuring? (y/N): ").strip().lower()
            if restart not in ['y', 'yes']:
                break

if __name__ == "__main__":
    main()
