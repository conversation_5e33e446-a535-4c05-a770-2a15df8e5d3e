"""
PDF Embedding System using Nomic Embeddings and PostgreSQL
"""

import os
import fitz  # PyMuPDF
import psycopg2
from psycopg2.extras import execute_values
import requests
import json
import logging
from typing import List, Dict, Tuple
from dotenv import load_dotenv
import hashlib
from pathlib import Path

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('pdf_embedder.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class NomicEmbedder:
    """Embedder using nomic-ai/nomic-embed-text-v1 via Nomic AI API"""

    def __init__(self, api_key: str = None, use_ollama: bool = False, ollama_base_url: str = "http://localhost:11434"):
        self.use_ollama = use_ollama
        
        if use_ollama:
            # Use local Ollama setup
            self.base_url = ollama_base_url
            self.model_name = "nomic-embed-text:latest"
            self.api_key = None
            logger.info(f"Initialized NomicEmbedder with Ollama - base_url: {self.base_url}, model: {self.model_name}")
        else:
            # Use Nomic AI Direct API
            self.api_key = api_key or os.getenv("NOMIC_API_KEY")
            self.base_url = "https://api-atlas.nomic.ai/v1"
            self.model_name = "nomic-embed-text-v1.5"

            if not self.api_key:
                logger.warning("No NOMIC_API_KEY found. Falling back to Ollama.")
                self.use_ollama = True
                self.base_url = ollama_base_url
                self.model_name = "nomic-embed-text:latest"
                self.api_key = None

            logger.info(f"Initialized NomicEmbedder with Nomic API - model: {self.model_name}")
        
    def get_embeddings(self, texts: List[str]) -> List[List[float]]:
        """Get embeddings for a list of texts using Nomic API or Ollama"""
        logger.info(f"Generating embeddings for {len(texts)} texts using {'Ollama' if self.use_ollama else 'Nomic API'}")
        embeddings = []

        for i, text in enumerate(texts):
            try:
                # Truncate very long texts to avoid issues
                original_length = len(text)
                max_length = 8000 if self.use_ollama else 8192  # Nomic API supports up to 8192 tokens
                if len(text) > max_length:
                    text = text[:max_length]
                    logger.debug(f"Truncated text {i} from {original_length} to {len(text)} characters")

                logger.debug(f"Requesting embedding for text {i} ({len(text)} chars)")

                if self.use_ollama:
                    # Ollama API format
                    response = requests.post(
                        f"{self.base_url}/api/embeddings",
                        json={
                            "model": self.model_name,
                            "prompt": text
                        },
                        timeout=30
                    )
                else:
                    # Nomic AI API format
                    response = requests.post(
                        f"{self.base_url}/embeddings",
                        headers={
                            "Authorization": f"Bearer {self.api_key}",
                            "Content-Type": "application/json"
                        },
                        json={
                            "model": self.model_name,
                            "texts": [text],
                            "task_type": "search_document"
                        },
                        timeout=30
                    )

                if response.status_code == 200:
                    result = response.json()

                    if self.use_ollama:
                        embedding = result.get("embedding")
                    else:
                        # Nomic API returns embeddings in 'embeddings' array
                        embeddings_data = result.get("embeddings", [])
                        embedding = embeddings_data[0] if embeddings_data else None

                    if embedding and len(embedding) > 0:
                        embeddings.append(embedding)
                        if i < 3:  # Log first few embeddings
                            logger.debug(f"Generated embedding {i} with dimension {len(embedding)}")
                    else:
                        logger.warning(f"Empty embedding returned for text {i}")
                        # Use a small random embedding instead of zeros to avoid NaN in similarity
                        import random
                        random_embedding = [random.uniform(-0.1, 0.1) for _ in range(768)]
                        embeddings.append(random_embedding)
                else:
                    logger.error(f"Error getting embedding for text {i}: {response.status_code} - {response.text}")
                    # Use a small random embedding instead of zeros
                    import random
                    random_embedding = [random.uniform(-0.1, 0.1) for _ in range(768)]
                    embeddings.append(random_embedding)

            except Exception as e:
                logger.error(f"Error processing text {i}: {e}", exc_info=True)
                # Use a small random embedding instead of zeros
                import random
                random_embedding = [random.uniform(-0.1, 0.1) for _ in range(768)]
                embeddings.append(random_embedding)

        logger.info(f"Generated {len(embeddings)} embeddings total")
        return embeddings

class OpenAICompatibleEmbedder:
    """Embedder using OpenAI-compatible API format (works with many providers)"""

    def __init__(self, api_key: str = None, base_url: str = None, model: str = None):
        # Default to OpenAI, but can be overridden for other providers
        self.api_key = api_key or os.getenv("OPENAI_API_KEY")
        self.base_url = base_url or os.getenv("OPENAI_BASE_URL", "https://api.openai.com/v1")
        self.model_name = model or os.getenv("EMBEDDING_MODEL", "text-embedding-3-small")

        logger.info(f"Initialized OpenAICompatibleEmbedder with base_url: {self.base_url}, model: {self.model_name}")

        if not self.api_key:
            logger.warning("No API key found for OpenAI-compatible embedder")

    def get_embeddings(self, texts: List[str]) -> List[List[float]]:
        """Get embeddings using OpenAI-compatible API"""
        logger.info(f"Generating embeddings for {len(texts)} texts using OpenAI-compatible API")
        embeddings = []

        try:
            # OpenAI API supports batch processing
            response = requests.post(
                f"{self.base_url}/embeddings",
                headers={
                    "Authorization": f"Bearer {self.api_key}",
                    "Content-Type": "application/json"
                },
                json={
                    "model": self.model_name,
                    "input": texts
                },
                timeout=60
            )

            if response.status_code == 200:
                result = response.json()
                data = result.get("data", [])

                for item in data:
                    embedding = item.get("embedding", [])
                    if embedding:
                        embeddings.append(embedding)
                    else:
                        logger.warning(f"Empty embedding in response")
                        import random
                        random_embedding = [random.uniform(-0.1, 0.1) for _ in range(1536)]  # OpenAI default dimension
                        embeddings.append(random_embedding)

                logger.info(f"Generated {len(embeddings)} embeddings successfully")

            else:
                logger.error(f"API error: {response.status_code} - {response.text}")
                # Return random embeddings as fallback
                for _ in texts:
                    import random
                    random_embedding = [random.uniform(-0.1, 0.1) for _ in range(1536)]
                    embeddings.append(random_embedding)

        except Exception as e:
            logger.error(f"Error getting embeddings: {e}", exc_info=True)
            # Return random embeddings as fallback
            for _ in texts:
                import random
                random_embedding = [random.uniform(-0.1, 0.1) for _ in range(1536)]
                embeddings.append(random_embedding)

        return embeddings

class HuggingFaceEmbedder:
    """Embedder using Hugging Face Inference API"""

    def __init__(self, api_key: str = None, model: str = None):
        self.api_key = api_key or os.getenv("HUGGINGFACE_API_KEY")
        self.model_name = model or os.getenv("EMBEDDING_MODEL", "sentence-transformers/all-MiniLM-L6-v2")
        # Correct HF Inference API URL format
        self.base_url = f"https://api-inference.huggingface.co/models/{self.model_name}"

        logger.info(f"Initialized HuggingFaceEmbedder with model: {self.model_name}")
        logger.info(f"Using API URL: {self.base_url}")

        if not self.api_key:
            logger.warning("No HUGGINGFACE_API_KEY found")

    def get_embeddings(self, texts: List[str]) -> List[List[float]]:
        """Get embeddings using Hugging Face Inference API"""
        logger.info(f"Generating embeddings for {len(texts)} texts using Hugging Face")
        embeddings = []

        for i, text in enumerate(texts):
            try:
                # Truncate text if too long
                if len(text) > 512:  # Most HF models have 512 token limit
                    text = text[:512]
                    logger.debug(f"Truncated text {i} to 512 characters for HF API")

                logger.debug(f"Requesting HF embedding for text {i}: {text[:50]}...")

                response = requests.post(
                    self.base_url,
                    headers={
                        "Authorization": f"Bearer {self.api_key}",
                        "Content-Type": "application/json"
                    },
                    json={
                        "inputs": text,
                        "options": {
                            "wait_for_model": True,
                            "use_cache": False
                        }
                    },
                    timeout=120  # Increased timeout for model loading
                )

                logger.debug(f"HF API response status: {response.status_code}")

                if response.status_code == 200:
                    result = response.json()
                    logger.debug(f"HF API response type: {type(result)}")

                    # Handle different response formats
                    if isinstance(result, list) and len(result) > 0:
                        # Direct embedding array
                        if isinstance(result[0], (int, float)):
                            embeddings.append(result)
                            logger.debug(f"Got direct embedding with dimension {len(result)}")
                        # Nested array (common format)
                        elif isinstance(result[0], list):
                            embeddings.append(result[0])
                            logger.debug(f"Got nested embedding with dimension {len(result[0])}")
                        else:
                            logger.warning(f"Unexpected HF response format for text {i}: {type(result[0])}")
                            import random
                            random_embedding = [random.uniform(-0.1, 0.1) for _ in range(384)]
                            embeddings.append(random_embedding)
                    else:
                        logger.warning(f"Invalid HF embedding format for text {i}: {type(result)}")
                        import random
                        random_embedding = [random.uniform(-0.1, 0.1) for _ in range(384)]
                        embeddings.append(random_embedding)

                elif response.status_code == 503:
                    logger.warning(f"HF model loading for text {i}, retrying in 10 seconds...")
                    import time
                    time.sleep(10)
                    # Retry once
                    response = requests.post(self.base_url, headers={"Authorization": f"Bearer {self.api_key}"}, json={"inputs": text}, timeout=120)
                    if response.status_code == 200:
                        result = response.json()
                        if isinstance(result, list) and len(result) > 0:
                            embeddings.append(result[0] if isinstance(result[0], list) else result)
                        else:
                            import random
                            random_embedding = [random.uniform(-0.1, 0.1) for _ in range(384)]
                            embeddings.append(random_embedding)
                    else:
                        logger.error(f"HF API retry failed for text {i}: {response.status_code} - {response.text}")
                        import random
                        random_embedding = [random.uniform(-0.1, 0.1) for _ in range(384)]
                        embeddings.append(random_embedding)
                else:
                    logger.error(f"HF API error for text {i}: {response.status_code} - {response.text}")
                    logger.error(f"Request URL: {self.base_url}")
                    logger.error(f"Model: {self.model_name}")
                    import random
                    random_embedding = [random.uniform(-0.1, 0.1) for _ in range(384)]
                    embeddings.append(random_embedding)

            except Exception as e:
                logger.error(f"Error processing text {i} with HF API: {e}", exc_info=True)
                import random
                random_embedding = [random.uniform(-0.1, 0.1) for _ in range(384)]
                embeddings.append(random_embedding)

        logger.info(f"Generated {len(embeddings)} embeddings using Hugging Face")
        return embeddings

class SentenceTransformersEmbedder:
    """Embedder using Sentence Transformers library (local processing)"""

    def __init__(self, model: str = None):
        self.model_name = model or os.getenv("EMBEDDING_MODEL", "all-MiniLM-L6-v2")
        self.model = None

        logger.info(f"Initialized SentenceTransformersEmbedder with model: {self.model_name}")

        try:
            # Try to import and load the model
            from sentence_transformers import SentenceTransformer
            logger.info(f"Loading Sentence Transformers model: {self.model_name}")
            self.model = SentenceTransformer(self.model_name)
            logger.info("Sentence Transformers model loaded successfully")
        except ImportError:
            logger.error("sentence-transformers library not installed. Install with: pip install sentence-transformers")
            self.model = None
        except Exception as e:
            logger.error(f"Error loading Sentence Transformers model: {e}")
            self.model = None

    def get_embeddings(self, texts: List[str]) -> List[List[float]]:
        """Get embeddings using Sentence Transformers"""
        logger.info(f"Generating embeddings for {len(texts)} texts using Sentence Transformers")

        if not self.model:
            logger.error("Sentence Transformers model not available, returning random embeddings")
            embeddings = []
            for _ in texts:
                import random
                random_embedding = [random.uniform(-0.1, 0.1) for _ in range(384)]
                embeddings.append(random_embedding)
            return embeddings

        try:
            # Generate embeddings in batch
            embeddings = self.model.encode(texts, convert_to_tensor=False, show_progress_bar=True)

            # Convert to list format
            embeddings_list = [embedding.tolist() for embedding in embeddings]

            logger.info(f"Generated {len(embeddings_list)} embeddings using Sentence Transformers")
            logger.debug(f"Embedding dimension: {len(embeddings_list[0]) if embeddings_list else 'N/A'}")

            return embeddings_list

        except Exception as e:
            logger.error(f"Error generating embeddings with Sentence Transformers: {e}", exc_info=True)
            # Return random embeddings as fallback
            embeddings = []
            for _ in texts:
                import random
                random_embedding = [random.uniform(-0.1, 0.1) for _ in range(384)]
                embeddings.append(random_embedding)
            return embeddings

class TogetherAIEmbedder:
    """Embedder using Together AI API"""

    def __init__(self, api_key: str = None, model: str = None):
        self.api_key = api_key or os.getenv("TOGETHER_API_KEY")
        self.base_url = "https://api.together.xyz/v1"
        self.model_name = model or os.getenv("EMBEDDING_MODEL", "togethercomputer/m2-bert-80M-8k-retrieval")

        logger.info(f"Initialized TogetherAIEmbedder with model: {self.model_name}")

        if not self.api_key:
            logger.warning("No TOGETHER_API_KEY found")

    def get_embeddings(self, texts: List[str]) -> List[List[float]]:
        """Get embeddings using Together AI API"""
        logger.info(f"Generating embeddings for {len(texts)} texts using Together AI")
        embeddings = []

        try:
            response = requests.post(
                f"{self.base_url}/embeddings",
                headers={
                    "Authorization": f"Bearer {self.api_key}",
                    "Content-Type": "application/json"
                },
                json={
                    "model": self.model_name,
                    "input": texts
                },
                timeout=60
            )

            if response.status_code == 200:
                result = response.json()
                data = result.get("data", [])

                for item in data:
                    embedding = item.get("embedding", [])
                    if embedding:
                        embeddings.append(embedding)
                    else:
                        logger.warning(f"Empty embedding in Together AI response")
                        import random
                        random_embedding = [random.uniform(-0.1, 0.1) for _ in range(768)]
                        embeddings.append(random_embedding)

                logger.info(f"Generated {len(embeddings)} embeddings using Together AI")

            else:
                logger.error(f"Together AI API error: {response.status_code} - {response.text}")
                for _ in texts:
                    import random
                    random_embedding = [random.uniform(-0.1, 0.1) for _ in range(768)]
                    embeddings.append(random_embedding)

        except Exception as e:
            logger.error(f"Error getting embeddings from Together AI: {e}", exc_info=True)
            for _ in texts:
                import random
                random_embedding = [random.uniform(-0.1, 0.1) for _ in range(768)]
                embeddings.append(random_embedding)

        return embeddings

class PDFProcessor:
    """Process PDFs and extract text with chunking strategy"""

    def __init__(self, chunk_size: int = 1000, chunk_overlap: int = 200):
        self.chunk_size = chunk_size
        self.chunk_overlap = chunk_overlap

        logger.info(f"Initialized PDFProcessor with chunk_size: {chunk_size}, chunk_overlap: {chunk_overlap}")
        
    def extract_text_from_pdf(self, pdf_path: str) -> str:
        """Extract text from PDF file"""
        logger.info(f"Extracting text from PDF: {pdf_path}")
        try:
            doc = fitz.open(pdf_path)
            text = ""

            logger.debug(f"PDF has {doc.page_count} pages")
            for page_num in range(doc.page_count):
                page = doc[page_num]
                page_text = page.get_text()
                text += page_text
                logger.debug(f"Extracted {len(page_text)} characters from page {page_num + 1}")

            doc.close()
            extracted_text = text.strip()
            logger.info(f"Successfully extracted {len(extracted_text)} characters from {pdf_path}")
            return extracted_text

        except Exception as e:
            logger.error(f"Error extracting text from {pdf_path}: {e}", exc_info=True)
            return ""
    
    def chunk_text(self, text: str, filename: str) -> List[Dict]:
        """Split text into chunks based on headings and logical structure"""
        if not text:
            return []

        chunks = []

        # First, try to split by markdown-style headings
        heading_chunks = self._split_by_headings(text, filename)
        if heading_chunks:
            return heading_chunks

        # If no clear headings, try to split by common medical document patterns
        pattern_chunks = self._split_by_medical_patterns(text, filename)
        if pattern_chunks:
            return pattern_chunks

        # Fallback to word-based chunking if no structure is found
        return self._split_by_words(text, filename)

    def _split_by_headings(self, text: str, filename: str) -> List[Dict]:
        """Split text based on markdown-style headings (# ## ###)"""
        chunks = []
        lines = text.split('\n')

        current_chunk = []
        current_heading = "Introduction"
        chunk_index = 0

        for line in lines:
            line = line.strip()

            # Check if line is a heading
            if line.startswith('#'):
                # Save previous chunk if it has content
                if current_chunk:
                    chunk_text = '\n'.join(current_chunk).strip()
                    if chunk_text:
                        chunk_id = hashlib.md5(f"{filename}_{chunk_index}_{current_heading}".encode()).hexdigest()
                        chunks.append({
                            "chunk_id": chunk_id,
                            "filename": filename,
                            "chunk_index": chunk_index,
                            "text": chunk_text,
                            "heading": current_heading,
                            "word_count": len(chunk_text.split())
                        })
                        chunk_index += 1

                # Start new chunk
                current_heading = line.replace('#', '').strip()
                current_chunk = [line]
            else:
                current_chunk.append(line)

        # Add the last chunk
        if current_chunk:
            chunk_text = '\n'.join(current_chunk).strip()
            if chunk_text:
                chunk_id = hashlib.md5(f"{filename}_{chunk_index}_{current_heading}".encode()).hexdigest()
                chunks.append({
                    "chunk_id": chunk_id,
                    "filename": filename,
                    "chunk_index": chunk_index,
                    "text": chunk_text,
                    "heading": current_heading,
                    "word_count": len(chunk_text.split())
                })

        return chunks if chunks else []

    def _split_by_medical_patterns(self, text: str, filename: str) -> List[Dict]:
        """Split text based on common medical document patterns"""
        chunks = []

        # Common medical section patterns
        section_patterns = [
            r'(?i)^(symptoms?|signs?):?\s*$',
            r'(?i)^(treatment|therapy):?\s*$',
            r'(?i)^(diagnosis|diagnostic):?\s*$',
            r'(?i)^(occurrence|epidemiology):?\s*$',
            r'(?i)^(prevention|prophylaxis):?\s*$',
            r'(?i)^(etiology|cause|causes):?\s*$',
            r'(?i)^(pathogenesis|pathology):?\s*$',
            r'(?i)^(prognosis|outcome):?\s*$',
            r'(?i)^(overview|introduction):?\s*$'
        ]

        import re
        lines = text.split('\n')

        current_chunk = []
        current_section = "Overview"
        chunk_index = 0

        for line in lines:
            line_stripped = line.strip()

            # Check if line matches any medical section pattern
            is_section_header = False
            for pattern in section_patterns:
                if re.match(pattern, line_stripped):
                    is_section_header = True
                    break

            if is_section_header:
                # Save previous chunk
                if current_chunk:
                    chunk_text = '\n'.join(current_chunk).strip()
                    if chunk_text:
                        chunk_id = hashlib.md5(f"{filename}_{chunk_index}_{current_section}".encode()).hexdigest()
                        chunks.append({
                            "chunk_id": chunk_id,
                            "filename": filename,
                            "chunk_index": chunk_index,
                            "text": chunk_text,
                            "heading": current_section,
                            "word_count": len(chunk_text.split())
                        })
                        chunk_index += 1

                # Start new section
                current_section = line_stripped.replace(':', '').strip()
                current_chunk = [line]
            else:
                current_chunk.append(line)

        # Add the last chunk
        if current_chunk:
            chunk_text = '\n'.join(current_chunk).strip()
            if chunk_text:
                chunk_id = hashlib.md5(f"{filename}_{chunk_index}_{current_section}".encode()).hexdigest()
                chunks.append({
                    "chunk_id": chunk_id,
                    "filename": filename,
                    "chunk_index": chunk_index,
                    "text": chunk_text,
                    "heading": current_section,
                    "word_count": len(chunk_text.split())
                })

        return chunks if chunks else []

    def _split_by_words(self, text: str, filename: str) -> List[Dict]:
        """Fallback word-based chunking"""
        chunks = []
        words = text.split()

        for i in range(0, len(words), self.chunk_size - self.chunk_overlap):
            chunk_words = words[i:i + self.chunk_size]
            chunk_text = " ".join(chunk_words)

            # Create chunk metadata
            chunk_id = hashlib.md5(f"{filename}_{i}_{chunk_text[:100]}".encode()).hexdigest()

            chunks.append({
                "chunk_id": chunk_id,
                "filename": filename,
                "chunk_index": len(chunks),
                "text": chunk_text,
                "heading": f"Section {len(chunks) + 1}",
                "word_count": len(chunk_words)
            })

        return chunks

class PostgreSQLManager:
    """Manage PostgreSQL database operations"""

    def __init__(self):
        # Check if DATABASE_URL is provided (for production deployments like Render)
        self.database_url = os.getenv("DATABASE_URL")

        if self.database_url:
            # Use DATABASE_URL for connection (production)
            self.db_config = None
            logger.info("Using DATABASE_URL for database connection (production mode)")
        else:
            # Use individual config parameters (development)
            self.db_config = {
                "dbname": os.getenv("DB_NAME", "kforce"),
                "user": os.getenv("DB_USER", "postgres"),
                "password": os.getenv("DB_PASS", "kforce"),
                "host": os.getenv("DB_HOST", "localhost"),
                "port": os.getenv("DB_PORT", "5444")
            }
            logger.info(f"Using individual DB config for connection (development mode): {self.db_config['host']}:{self.db_config['port']}/{self.db_config['dbname']}")

        self.table_name = os.getenv("DB_TABLE_NAME", "documents")
        logger.info(f"Using table name: {self.table_name}")

    def get_connection(self):
        """Get database connection with retry logic"""
        import time
        max_retries = 3
        retry_delay = 2

        logger.debug("Attempting to establish database connection...")
        for attempt in range(max_retries):
            try:
                if self.database_url:
                    # Use DATABASE_URL (production)
                    logger.debug(f"Connecting using DATABASE_URL (attempt {attempt + 1})")
                    conn = psycopg2.connect(self.database_url)
                else:
                    # Use individual parameters (development)
                    logger.debug(f"Connecting using individual parameters (attempt {attempt + 1})")
                    conn = psycopg2.connect(**self.db_config)

                # Test the connection
                with conn.cursor() as cur:
                    cur.execute("SELECT 1")
                    cur.fetchone()

                logger.info(f"Database connection established successfully on attempt {attempt + 1}")
                return conn

            except Exception as e:
                logger.warning(f"Database connection attempt {attempt + 1} failed: {e}")
                if attempt < max_retries - 1:
                    logger.info(f"Retrying in {retry_delay} seconds...")
                    time.sleep(retry_delay)
                    retry_delay *= 2  # Exponential backoff
                else:
                    logger.error("All database connection attempts failed")
                    raise
    
    def create_tables(self, embedding_dimension: int = 768):
        """Create necessary tables for storing embeddings with dynamic dimensions"""
        logger.info(f"Creating tables for {self.table_name} with {embedding_dimension} dimensions...")

        create_table_sql = f"""
        -- Enable pgvector extension
        CREATE EXTENSION IF NOT EXISTS vector;

        -- Create documents table
        CREATE TABLE IF NOT EXISTS {self.table_name} (
            id SERIAL PRIMARY KEY,
            chunk_id VARCHAR(255) UNIQUE NOT NULL,
            filename VARCHAR(255) NOT NULL,
            chunk_index INTEGER NOT NULL,
            content TEXT NOT NULL,
            heading VARCHAR(255),
            word_count INTEGER,
            embedding vector({embedding_dimension}),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );

        -- Create index for vector similarity search
        CREATE INDEX IF NOT EXISTS {self.table_name}_embedding_idx
        ON {self.table_name} USING ivfflat (embedding vector_cosine_ops)
        WITH (lists = 100);

        -- Create index for filename searches
        CREATE INDEX IF NOT EXISTS {self.table_name}_filename_idx ON {self.table_name}(filename);
        """

        try:
            with self.get_connection() as conn:
                with conn.cursor() as cur:
                    logger.debug("Executing table creation SQL...")
                    cur.execute(create_table_sql)
                    conn.commit()
                    logger.info(f"Tables created successfully with {embedding_dimension} dimensions!")

        except Exception as e:
            logger.error(f"Error creating tables: {e}", exc_info=True)

    def get_embedding_dimension(self):
        """Get the current embedding dimension from the database"""
        try:
            with self.get_connection() as conn:
                with conn.cursor() as cur:
                    # Check if table exists
                    cur.execute("""
                        SELECT EXISTS (
                            SELECT FROM information_schema.tables
                            WHERE table_name = %s
                        );
                    """, (self.table_name,))

                    if not cur.fetchone()[0]:
                        logger.info("Table doesn't exist yet")
                        return None

                    # Get vector dimension from table schema
                    cur.execute("""
                        SELECT atttypmod
                        FROM pg_attribute
                        WHERE attrelid = %s::regclass
                        AND attname = 'embedding'
                    """, (self.table_name,))

                    result = cur.fetchone()
                    if result and result[0] > 0:
                        dimension = result[0]
                        logger.info(f"Current database embedding dimension: {dimension}")
                        return dimension
                    else:
                        logger.warning("Could not determine embedding dimension from database")
                        return None

        except Exception as e:
            logger.error(f"Error getting embedding dimension: {e}", exc_info=True)
            return None

    def check_dimension_compatibility(self, new_dimension: int):
        """Check if new embedding dimension is compatible with existing data"""
        current_dim = self.get_embedding_dimension()

        if current_dim is None:
            logger.info("No existing table, dimension check passed")
            return True, None

        if current_dim == new_dimension:
            logger.info(f"Dimension compatibility check passed: {new_dimension}")
            return True, current_dim
        else:
            logger.warning(f"Dimension mismatch: database has {current_dim}, new embeddings have {new_dimension}")
            return False, current_dim
    
    def insert_documents(self, chunks: List[Dict], embeddings: List[List[float]]):
        """Insert document chunks and embeddings into database"""
        logger.info(f"Inserting {len(chunks)} document chunks into database...")

        if len(chunks) != len(embeddings):
            error_msg = f"Number of chunks ({len(chunks)}) and embeddings ({len(embeddings)}) must match"
            logger.error(error_msg)
            raise ValueError(error_msg)

        insert_sql = f"""
        INSERT INTO {self.table_name} (chunk_id, filename, chunk_index, content, heading, word_count, embedding)
        VALUES %s
        ON CONFLICT (chunk_id) DO UPDATE SET
            content = EXCLUDED.content,
            heading = EXCLUDED.heading,
            word_count = EXCLUDED.word_count,
            embedding = EXCLUDED.embedding,
            created_at = CURRENT_TIMESTAMP
        """

        # Prepare data for insertion
        logger.debug("Preparing data for insertion...")
        data = []
        for i, (chunk, embedding) in enumerate(zip(chunks, embeddings)):
            # Validate embedding
            if not embedding or len(embedding) == 0:
                logger.warning(f"Empty embedding for chunk {i}, using random embedding")
                import random
                embedding = [random.uniform(-0.1, 0.1) for _ in range(768)]

            data.append((
                chunk["chunk_id"],
                chunk["filename"],
                chunk["chunk_index"],
                chunk["text"],
                chunk.get("heading", "Unknown"),
                chunk["word_count"],
                embedding
            ))

        try:
            with self.get_connection() as conn:
                with conn.cursor() as cur:
                    logger.debug(f"Executing batch insert with {len(data)} records...")
                    execute_values(cur, insert_sql, data, template=None, page_size=100)
                    conn.commit()
                    logger.info(f"Successfully inserted {len(data)} document chunks")

        except Exception as e:
            logger.error(f"Error inserting documents: {e}", exc_info=True)
    
    def get_document_count(self) -> int:
        """Get total number of documents in database"""
        try:
            with self.get_connection() as conn:
                with conn.cursor() as cur:
                    cur.execute(f"SELECT COUNT(*) FROM {self.table_name}")
                    return cur.fetchone()[0]
        except Exception as e:
            print(f"Error getting document count: {e}")
            return 0

class PDFEmbeddingPipeline:
    """Main pipeline for processing PDFs and creating embeddings"""

    def __init__(self, embedder_type: str = "nomic"):
        logger.info(f"Initializing PDFEmbeddingPipeline with embedder type: {embedder_type}")

        # Choose embedder based on type
        embedder_type_lower = embedder_type.lower()

        if embedder_type_lower == "openai":
            self.embedder = OpenAICompatibleEmbedder()
        elif embedder_type_lower == "nomic_api":
            self.embedder = NomicEmbedder(use_ollama=False)
        elif embedder_type_lower == "huggingface" or embedder_type_lower == "hf":
            self.embedder = HuggingFaceEmbedder()
        elif embedder_type_lower == "sentence_transformers" or embedder_type_lower == "st":
            self.embedder = SentenceTransformersEmbedder()
        elif embedder_type_lower == "together" or embedder_type_lower == "together_ai":
            self.embedder = TogetherAIEmbedder()
        elif embedder_type_lower == "ollama" or embedder_type_lower == "nomic":
            self.embedder = NomicEmbedder(use_ollama=True)
        else:
            logger.warning(f"Unknown embedder type: {embedder_type}, defaulting to Nomic with Ollama")
            self.embedder = NomicEmbedder(use_ollama=True)

        self.processor = PDFProcessor()
        self.db_manager = PostgreSQLManager()
        logger.info("PDFEmbeddingPipeline initialized successfully")
        
    def process_pdf_folder(self, folder_path: str):
        """Process all PDFs in a folder"""
        logger.info(f"Starting to process PDF folder: {folder_path}")
        folder_path = Path(folder_path)

        if not folder_path.exists():
            error_msg = f"Folder {folder_path} does not exist"
            logger.error(error_msg)
            return

        # Test embedding dimension with a sample text
        logger.info("Testing embedding dimension...")
        test_embeddings = self.embedder.get_embeddings(["test"])
        if not test_embeddings or not test_embeddings[0]:
            logger.error("Failed to generate test embedding")
            return

        embedding_dim = len(test_embeddings[0])
        logger.info(f"Embedder produces {embedding_dim}-dimensional vectors")

        # Check dimension compatibility
        compatible, current_dim = self.db_manager.check_dimension_compatibility(embedding_dim)

        if not compatible:
            logger.error(f"Dimension mismatch! Database expects {current_dim}D but embedder produces {embedding_dim}D")
            logger.error("Solutions:")
            logger.error(f"1. Drop and recreate table: DROP TABLE {self.db_manager.table_name};")
            logger.error(f"2. Use a {current_dim}-dimensional embedding model")
            logger.error("3. Create a new table with different name")
            return

        # Create database tables with correct dimension
        self.db_manager.create_tables(embedding_dimension=embedding_dim)

        # Get all PDF files
        pdf_files = list(folder_path.glob("*.pdf"))
        logger.info(f"Found {len(pdf_files)} PDF files in {folder_path}")

        for i, pdf_file in enumerate(pdf_files, 1):
            logger.info(f"Processing file {i}/{len(pdf_files)}: {pdf_file.name}")
            self.process_single_pdf(str(pdf_file))

        total_docs = self.db_manager.get_document_count()
        logger.info(f"Processing complete! Total documents in database: {total_docs}")
    
    def process_single_pdf(self, pdf_path: str):
        """Process a single PDF file"""
        filename = Path(pdf_path).name
        logger.info(f"Processing single PDF: {filename}")

        # Extract text
        text = self.processor.extract_text_from_pdf(pdf_path)
        if not text:
            logger.warning(f"No text extracted from {filename}")
            return

        # Create chunks
        logger.debug(f"Creating chunks from {len(text)} characters of text")
        chunks = self.processor.chunk_text(text, filename)
        if not chunks:
            logger.warning(f"No chunks created from {filename}")
            return

        logger.info(f"Created {len(chunks)} chunks from {filename}")

        # Get embeddings
        logger.debug("Extracting text from chunks for embedding generation")
        texts = [chunk["text"] for chunk in chunks]
        embeddings = self.embedder.get_embeddings(texts)

        # Validate embeddings
        if len(embeddings) != len(chunks):
            logger.error(f"Embedding count ({len(embeddings)}) doesn't match chunk count ({len(chunks)})")
            return

        # Store in database
        self.db_manager.insert_documents(chunks, embeddings)
        logger.info(f"Successfully processed and stored {len(chunks)} chunks for {filename}")

if __name__ == "__main__":
    # Initialize pipeline
    pipeline = PDFEmbeddingPipeline()
    
    # Process diseases folder
    diseases_folder = "diseases"
    pipeline.process_pdf_folder(diseases_folder)
