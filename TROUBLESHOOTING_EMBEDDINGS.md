# 🔧 Embedding Services Troubleshooting Guide

## ❌ Common Issues and Solutions

### 1. **Hugging Face 404 Error**
```
ERROR - HF API error for text 0: 404 - Not Found
```

**Causes:**
- Invalid model name
- Model not available via Inference API
- Incorrect API endpoint

**Solutions:**

#### Option A: Use Sentence Transformers (Recommended)
```bash
# Install the library
pip install sentence-transformers

# Update your .env
EMBEDDER_TYPE=sentence_transformers
EMBEDDING_MODEL=all-MiniLM-L6-v2
```

#### Option B: Use Working HF Models
```bash
# Try these verified models
EMBEDDER_TYPE=huggingface
EMBEDDING_MODEL=sentence-transformers/all-MiniLM-L6-v2
# or
EMBEDDING_MODEL=sentence-transformers/all-mpnet-base-v2
```

#### Option C: Switch to Nomic AI
```bash
EMBEDDER_TYPE=nomic_api
NOMIC_API_KEY=your_nomic_api_key
```

---

### 2. **API Key Issues**
```
ERROR - No API key found
WARNING - No NOMIC_API_KEY found
```

**Solution:**
```bash
# Run the configuration script
python configure_embeddings.py

# Or manually set in .env
echo "NOMIC_API_KEY=your_key_here" >> .env
echo "EMBEDDER_TYPE=nomic_api" >> .env
```

---

### 3. **Ollama Connection Issues**
```
ERROR - Error processing text: Connection refused
```

**Solutions:**
```bash
# Check if Ollama is running
ollama serve

# Pull the embedding model
ollama pull nomic-embed-text

# Or switch to cloud service
python configure_embeddings.py
```

---

### 4. **Rate Limiting**
```
ERROR - API error: 429 - Too Many Requests
```

**Solutions:**
- **Hugging Face**: Upgrade to Pro account or use Sentence Transformers locally
- **OpenAI**: Check your usage limits and billing
- **Nomic AI**: Contact support for rate limit increase

---

### 5. **Model Loading Timeout**
```
ERROR - API error: 503 - Service Unavailable
```

**For Hugging Face:**
```bash
# The model is loading, wait and retry
# Or use a pre-loaded model:
EMBEDDING_MODEL=sentence-transformers/all-MiniLM-L6-v2
```

---

## 🎯 **Recommended Solutions by Priority**

### **1st Choice: Nomic AI** (Most Reliable)
```bash
# Get API key from: https://atlas.nomic.ai/
export NOMIC_API_KEY="your_key"
export EMBEDDER_TYPE="nomic_api"
```

### **2nd Choice: Sentence Transformers** (Local, No API)
```bash
pip install sentence-transformers
export EMBEDDER_TYPE="sentence_transformers"
export EMBEDDING_MODEL="all-MiniLM-L6-v2"
```

### **3rd Choice: OpenAI** (Enterprise Grade)
```bash
export OPENAI_API_KEY="your_key"
export EMBEDDER_TYPE="openai"
```

### **4th Choice: Together AI** (Fast & Cheap)
```bash
export TOGETHER_API_KEY="your_key"
export EMBEDDER_TYPE="together"
```

---

## 🔍 **Debugging Steps**

### Step 1: Check Current Configuration
```bash
python -c "
import os
from dotenv import load_dotenv
load_dotenv()
print('EMBEDDER_TYPE:', os.getenv('EMBEDDER_TYPE'))
print('API Keys:', {k:v[:8]+'...' if v else None for k,v in os.environ.items() if 'API_KEY' in k})
"
```

### Step 2: Test Embedder Directly
```python
# Create test_embedder.py
from pdf_embedder import *
import os
from dotenv import load_dotenv

load_dotenv()
embedder_type = os.getenv("EMBEDDER_TYPE", "nomic")

if embedder_type == "nomic_api":
    embedder = NomicEmbedder(use_ollama=False)
elif embedder_type == "sentence_transformers":
    embedder = SentenceTransformersEmbedder()
elif embedder_type == "openai":
    embedder = OpenAICompatibleEmbedder()
else:
    embedder = NomicEmbedder(use_ollama=True)

# Test
result = embedder.get_embeddings(["test text"])
print(f"Success! Generated embedding with {len(result[0])} dimensions")
```

### Step 3: Run Debug Script
```bash
python debug_similarity.py
```

---

## 🚀 **Quick Fix Commands**

### Fix Hugging Face Issues:
```bash
# Switch to Sentence Transformers
pip install sentence-transformers
echo "EMBEDDER_TYPE=sentence_transformers" >> .env
echo "EMBEDDING_MODEL=all-MiniLM-L6-v2" >> .env
```

### Fix API Key Issues:
```bash
# Interactive setup
python configure_embeddings.py
```

### Fix Ollama Issues:
```bash
# Switch to Nomic API
echo "EMBEDDER_TYPE=nomic_api" >> .env
echo "NOMIC_API_KEY=your_key_here" >> .env
```

---

## 📊 **Service Status Check**

### Test Each Service:
```bash
# Test Nomic API
curl -H "Authorization: Bearer YOUR_KEY" \
  https://api-atlas.nomic.ai/v1/embeddings \
  -d '{"model":"nomic-embed-text-v1.5","texts":["test"]}'

# Test OpenAI
curl -H "Authorization: Bearer YOUR_KEY" \
  https://api.openai.com/v1/embeddings \
  -d '{"model":"text-embedding-3-small","input":"test"}'

# Test Hugging Face
curl -H "Authorization: Bearer YOUR_TOKEN" \
  https://api-inference.huggingface.co/models/sentence-transformers/all-MiniLM-L6-v2 \
  -d '{"inputs":"test"}'
```

---

## 💡 **Best Practices**

1. **Always have a fallback**: Configure multiple embedding services
2. **Use local models for development**: Sentence Transformers for testing
3. **Monitor API usage**: Set up billing alerts
4. **Cache embeddings**: Store results to avoid re-computation
5. **Test before production**: Use the debug script to validate setup

---

## 🆘 **Emergency Fallback**

If all else fails, use this minimal working configuration:

```bash
# Install sentence-transformers
pip install sentence-transformers

# Update .env
cat > .env << EOF
EMBEDDER_TYPE=sentence_transformers
EMBEDDING_MODEL=all-MiniLM-L6-v2
GROQ_API_KEY=your_groq_key_here
DB_NAME=kforce
DB_USER=postgres
DB_PASS=kforce
DB_HOST=localhost
DB_PORT=5444
EOF

# Restart application
python flask_app.py
```

This will use local Sentence Transformers (no API required) and should work immediately!
